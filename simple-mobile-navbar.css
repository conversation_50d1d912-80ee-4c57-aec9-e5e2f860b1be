/* Professional Simple Mobile Navbar */

/* Mobile Menu Button - Clean & Professional */
@media (max-width: 768px) {
    .mobile-menu-btn {
        display: flex !important;
        align-items: center;
        justify-content: center;
        width: 44px;
        height: 44px;
        background: #687FE5;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        z-index: 1001;
    }

    .mobile-menu-btn:hover {
        background: #5a6fd8;
        transform: translateY(-1px);
    }

    .mobile-menu-btn i {
        font-size: 1.5rem;
        color: white;
        transition: all 0.3s ease;
    }

    .desktop-menu {
        display: none !important;
    }
}

/* Mobile Menu Container - Clean & Simple */
.mobile-menu {
    display: none;
    position: fixed;
    top: -100%;
    left: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 10001;
    transition: top 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.mobile-menu.active {
    top: 0;
    display: block;
}

/* Mobile Menu Header - Clean & Professional */
.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    background: #687FE5;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-logo {
    height: 50px;
    filter: brightness(0) invert(1);
}

#menu-close {
    font-size: 1.5rem;
    color: white;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#menu-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg);
}

/* Mobile Navigation Links - Clean & Simple */
.mobile-nav-links {
    list-style: none;
    padding: 20px 0;
    margin: 0;
    background: transparent;
}

.mobile-nav-links li {
    margin: 0;
}

.mobile-nav-links li a {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 16px 25px;
    text-decoration: none;
    color: #333;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    background: transparent;
    border-bottom: 1px solid rgba(233, 236, 239, 0.5);
}

.mobile-nav-links li a:hover {
    background: rgba(104, 127, 229, 0.1);
    color: #687FE5;
}

.mobile-nav-links li a.active {
    background: rgba(104, 127, 229, 0.2);
    color: #687FE5;
    font-weight: 600;
}

.mobile-nav-links li a i {
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
    color: #687FE5;
    transition: all 0.3s ease;
}

.mobile-nav-links li a:hover i {
    color: #687FE5;
}

.mobile-nav-links li a.active i {
    color: white;
}

/* Services Dropdown - Clean & Simple */
.mobile-services-item {
    position: relative;
}

.mobile-services-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
    padding: 16px 25px;
    text-decoration: none;
    color: #333;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    background: transparent;
    border-bottom: 1px solid rgba(233, 236, 239, 0.5);
}

.mobile-services-toggle:hover {
    background: rgba(104, 127, 229, 0.1);
    color: #687FE5;
}

.mobile-services-toggle i:first-child {
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
    color: #687FE5;
    transition: all 0.3s ease;
}

.mobile-services-toggle:hover i:first-child {
    color: #687FE5;
}

.mobile-services-toggle .dropdown-arrow {
    font-size: 1rem;
    color: #687FE5;
    transition: all 0.3s ease;
}

.mobile-services-toggle:hover .dropdown-arrow {
    color: #687FE5;
}

.mobile-services-item.active .dropdown-arrow {
    transform: rotate(180deg);
    color: #687FE5;
}

.mobile-services-item.active .mobile-services-toggle {
    background: rgba(104, 127, 229, 0.2);
    color: #687FE5;
    font-weight: 600;
}

.mobile-services-dropdown {
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    background: rgba(241, 243, 244, 0.3);
}

.mobile-services-item.active .mobile-services-dropdown {
    max-height: 300px;
}

.mobile-services-dropdown a {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 14px 25px 14px 40px;
    text-decoration: none;
    color: #555;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    background: transparent;
    border-bottom: 1px solid rgba(233, 236, 239, 0.3);
}

.mobile-services-dropdown a:hover {
    background: rgba(104, 127, 229, 0.1);
    color: #687FE5;
}

.mobile-services-dropdown a.active {
    background: rgba(104, 127, 229, 0.2);
    color: #687FE5;
    font-weight: 600;
}

.mobile-services-dropdown a i {
    font-size: 1rem;
    width: 20px;
    text-align: center;
    color: #687FE5;
    transition: all 0.3s ease;
}

.mobile-services-dropdown a:hover i {
    color: #687FE5;
}

.mobile-services-dropdown a.active i {
    color: white;
}

/* Mobile Apply Button - Clean & Simple */
.mobile-apply-btn {
    margin: 20px 25px;
    padding: 14px 25px;
    background: #687FE5;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: calc(100% - 50px);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.mobile-apply-btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.mobile-apply-btn a {
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
}

.mobile-apply-btn i {
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.mobile-apply-btn:hover i {
    transform: translateX(2px);
}

/* Responsive Design */
@media (max-width: 480px) {
    .mobile-menu-header {
        padding: 15px 20px;
    }

    .mobile-logo {
        height: 45px;
    }

    .mobile-nav-links li a {
        padding: 14px 20px;
        font-size: 0.95rem;
    }

    .mobile-services-toggle {
        padding: 14px 20px;
        font-size: 0.95rem;
    }

    .mobile-services-dropdown a {
        padding: 12px 20px 12px 35px;
        font-size: 0.85rem;
    }

    .mobile-apply-btn {
        margin: 15px 20px;
        padding: 12px 20px;
        width: calc(100% - 40px);
    }
}
