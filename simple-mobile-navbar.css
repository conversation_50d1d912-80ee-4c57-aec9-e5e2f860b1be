/* Professional Simple Mobile Navbar */

/* Mobile Menu Button - Clean & Professional */
@media (max-width: 768px) {
    .mobile-menu-btn {
        display: flex !important;
        align-items: center;
        justify-content: center;
        width: 44px;
        height: 44px;
        background: #687FE5;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        z-index: 1001;
    }

    .mobile-menu-btn:hover {
        background: #5a6fd8;
        transform: translateY(-1px);
    }

    .mobile-menu-btn i {
        font-size: 1.5rem;
        color: white;
        transition: all 0.3s ease;
    }

    .desktop-menu {
        display: none !important;
    }
}

/* Mobile Menu Container - Clean White */
.mobile-menu {
    display: none;
    position: fixed;
    top: -100%;
    left: 0;
    width: 100%;
    background: white;
    z-index: 10001;
    transition: top 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.mobile-menu.active {
    top: 0;
    display: block;
}

/* Mobile Menu Header - Clean White */
.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 25px;
    background: white;
    border-bottom: 1px solid #e9ecef;
}

.mobile-logo {
    height: 70px;
    filter: none;
}

#menu-close {
    font-size: 1.5rem;
    color: #687FE5;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
    background: rgba(104, 127, 229, 0.1);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#menu-close:hover {
    background: rgba(104, 127, 229, 0.2);
    transform: rotate(90deg);
}

/* Mobile Navigation Links - Clean White */
.mobile-nav-links {
    list-style: none;
    padding: 20px 0;
    margin: 0;
    background: white;
}

.mobile-nav-links li {
    margin: 0;
}

.mobile-nav-links li a {
    display: block;
    padding: 18px 25px;
    text-decoration: none;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    background: transparent;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin: 8px 20px;
}

.mobile-nav-links li a:hover {
    background: rgba(104, 127, 229, 0.05);
    color: #687FE5;
    border-color: #687FE5;
    transform: translateX(5px);
}

.mobile-nav-links li a.active {
    background: #687FE5;
    color: white;
    font-weight: 700;
    border-color: #687FE5;
}

/* Icons removed for cleaner look */
.mobile-nav-links li a i {
    display: none;
}

/* Services Dropdown - Clean & Simple */
.mobile-services-item {
    position: relative;
}

.mobile-services-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
    padding: 18px 25px;
    text-decoration: none;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
    background: transparent;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin: 8px 20px;
}

.mobile-services-toggle:hover {
    background: rgba(104, 127, 229, 0.05);
    color: #687FE5;
    border-color: #687FE5;
    transform: translateX(5px);
}

/* Service icon removed for cleaner look */
.mobile-services-toggle i:first-child {
    display: none;
}

.mobile-services-toggle .dropdown-arrow {
    font-size: 1rem;
    color: #687FE5;
    transition: all 0.3s ease;
}

.mobile-services-toggle:hover .dropdown-arrow {
    color: #687FE5;
}

.mobile-services-item.active .dropdown-arrow {
    transform: rotate(180deg);
    color: #687FE5;
}

.mobile-services-item.active .mobile-services-toggle {
    background: #687FE5;
    color: white;
    font-weight: 700;
    border-color: #687FE5;
}

.mobile-services-dropdown {
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.mobile-services-item.active .mobile-services-dropdown {
    max-height: 300px;
}

.mobile-services-dropdown a {
    display: block;
    padding: 15px 25px 15px 35px;
    text-decoration: none;
    color: #555;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    background: transparent;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin: 6px 25px;
}

.mobile-services-dropdown a:hover {
    background: rgba(104, 127, 229, 0.05);
    color: #687FE5;
    border-color: #687FE5;
    transform: translateX(5px);
}

.mobile-services-dropdown a.active {
    background: #687FE5;
    color: white;
    font-weight: 700;
    border-color: #687FE5;
}

/* Dropdown icons removed for cleaner look */
.mobile-services-dropdown a i {
    display: none;
}

/* Mobile Apply Button - Enhanced */
.mobile-apply-btn {
    margin: 25px 20px 30px;
    padding: 16px 25px;
    background: linear-gradient(135deg, #687FE5, #5a6fd8);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    width: calc(100% - 40px);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    box-shadow: 0 4px 15px rgba(104, 127, 229, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mobile-apply-btn:hover {
    background: linear-gradient(135deg, #5a6fd8, #4c63d2);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(104, 127, 229, 0.4);
}

.mobile-apply-btn a {
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 700;
}

.mobile-apply-btn i {
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.mobile-apply-btn:hover i {
    transform: translateX(3px) scale(1.1);
}

/* Responsive Design */
@media (max-width: 480px) {
    .mobile-menu-header {
        padding: 20px 20px;
    }

    .mobile-logo {
        height: 60px;
    }

    .mobile-nav-links li a {
        padding: 14px 20px;
        font-size: 0.95rem;
    }

    .mobile-services-toggle {
        padding: 14px 20px;
        font-size: 0.95rem;
    }

    .mobile-services-dropdown a {
        padding: 12px 20px 12px 35px;
        font-size: 0.85rem;
    }

    .mobile-apply-btn {
        margin: 15px 20px;
        padding: 12px 20px;
        width: calc(100% - 40px);
    }
}
