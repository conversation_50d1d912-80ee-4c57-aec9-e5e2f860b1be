/* Global Styles */
*{
margin: 0;
padding: 0;
box-sizing: border-box;
}

/* Locomotive Scroll Styles */
html {
    scroll-behavior: auto;
}

html.has-scroll-smooth {
    overflow: hidden;
}

html.has-scroll-dragging {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.has-scroll-smooth body {
    overflow: hidden;
}

.has-scroll-smooth [data-scroll-container] {
    min-height: 100vh;
}

/* Custom Scrollbar */
.c-scrollbar {
    position: absolute;
    right: 0;
    top: 0;
    width: 11px;
    height: 100%;
    transform-origin: center right;
    transition: transform 0.3s, opacity 0.3s;
    opacity: 0;
}

.c-scrollbar:hover {
    transform: scaleX(1.45);
}

.c-scrollbar:hover,
.has-scroll-scrolling .c-scrollbar,
.has-scroll-dragging .c-scrollbar {
    opacity: 1;
}

.c-scrollbar_thumb {
    position: absolute;
    top: 0;
    right: 0;
    background-color: rgba(59, 130, 246, 0.6);
    opacity: 0.8;
    width: 7px;
    border-radius: 10px;
    margin: 2px;
    cursor: -webkit-grab;
    cursor: grab;
}

.has-scroll-dragging .c-scrollbar_thumb {
    cursor: -webkit-grabbing;
    cursor: grabbing;
}

/* Smooth Scroll Animations */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.is-revealed {
    opacity: 1;
    transform: translateY(0);
}

.slide-up {
    opacity: 0;
    transform: translateY(60px);
    transition: all 0.8s ease;
}

.slide-up.is-revealed {
    opacity: 1;
    transform: translateY(0);
}

.scale-in {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.6s ease;
}

.scale-in.is-revealed {
    opacity: 1;
    transform: scale(1);
}

/* Prevent horizontal scroll for specific elements only */
section, .container, .content {
    max-width: 100vw;
    overflow-x: hidden;
}

/* Ensure no element exceeds viewport width */
body, html {
    max-width: 100vw;
    overflow-x: hidden;
}

/* Main container */
.main {
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
    position: relative;
}

/* Internal Navigation Styles */
.internal-nav {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    padding: 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
}

.internal-nav .logo {
    height: 60px;
    filter: drop-shadow(2px 2px 6px rgba(0, 0, 0, 0.1));
}

.internal-nav .desktop-menu ul {
    display: flex;
    align-items: center;
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.internal-nav .desktop-menu ul li a {
    color: #333;
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.internal-nav .desktop-menu ul li a:hover,
.internal-nav .desktop-menu ul li a.active {
    color: navy;
    background: rgba(0, 0, 139, 0.1);
}

/* Page Header Styles */
.page-header {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    color: white;
    padding: 100px 0 60px;
    text-align: center;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.header-content h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 1rem;
}

.breadcrumb a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb a:hover {
    color: white;
}

.breadcrumb i {
    color: rgba(255,255,255,0.6);
}

html{
scroll-behavior: smooth;
}



html,body{
height: 100%;
width: 100%;
font-family: 'Poppins', sans-serif;
overflow-x: hidden;
margin: 0;
padding: 0;
box-sizing: border-box;
}
.hero{
height: 100vh;
width: 100%;
background: url(images/hero/Landing\ World\ Map.jpg);
background-size: cover;
background-position: center;
position: relative;
overflow-x: hidden;
}
    nav{
        height: 12vh;
        width: 100%;
        background: none;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 2%;
        position: relative;
        z-index: 1000;
        border-bottom: 0.5px solid rgba(0, 0, 170, 0.329);
    }
    nav-div{
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    nav img{
        height: 80px;
        filter: drop-shadow(2px 2px 6px rgba(255, 255, 255, 0.8));
    }
    ul {
        display: flex;
        align-items: center;
        justify-content: space-between;
        list-style: none;
        gap: 3rem;
    }

    ul button{
        padding: 0.7rem 1.5rem;
        border-radius: 20px;
        background-color: navy;
        color: white;
        cursor: pointer;
        font-size: 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
        border: none;
        text-decoration: none;
    }
        ul button a{
            text-decoration: none;
            color: white;
        }
    /* Desktop Menu - Default Visible */
    .desktop-menu {
        display: block;
    }

    /* Professional Mobile Menu Styles - Hidden by Default */
    .mobile-menu-btn {
        display: none;
        cursor: pointer;
        font-size: 1.8rem;
        color: var(--primary);
        padding: 10px;
        border-radius: 12px;
        transition: all 0.3s ease;
        background: rgba(0, 0, 128, 0.08);
        border: 1px solid rgba(0, 0, 128, 0.15);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .mobile-menu-btn:hover {
        background: var(--primary);
        color: white;
        transform: scale(1.05);
        box-shadow: 0 4px 15px rgba(0, 0, 128, 0.3);
    }

    .mobile-menu {
        display: none;
        position: fixed;
        top: -100%;
        left: 0;
        width: 100%;
        background: #BFECFF;
        z-index: 10001;
        transition: top 0.4s ease-in-out;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid rgba(0, 0, 139, 0.2);
    }

    #mobile-menu {
        background-color: #BFECFF;
    }



    .mobile-menu.active {
        top: 0;
    }

    .mobile-menu-content {
        padding: 0;
        max-height: 100vh;
        overflow-y: auto;
    }

    .mobile-menu-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem 2rem;
        background: #BFECFF;
        border-bottom: 1px solid rgba(0, 0, 139, 0.1);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }



    .mobile-logo {
        height: 50px;
        filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.1));
        transition: transform 0.3s ease;
    }

    .mobile-logo:hover {
        transform: scale(1.05);
    }

    #menu-close {
        font-size: 1.8rem;
        color: navy;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 50%;
        transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        display: flex;
        align-items: center;
        justify-content: center;
        width: 45px;
        height: 45px;
        background: linear-gradient(135deg, rgba(255, 71, 87, 0.1) 0%, rgba(255, 71, 87, 0.05) 100%);
    }

    #menu-close:hover {
        background: navy;
        color: white;
        transform: rotate(180deg) scale(1.1);
    }

    .mobile-nav-links {
        list-style: none;
        padding: 1rem 0;
        margin: 0;
        display: flex;
        flex-direction: column;
        gap: 0;
        background: #BFECFF;
    }

    .mobile-nav-links li {
        border-bottom: 1px solid rgba(0, 0, 139, 0.1);
        transition: all 0.3s ease;
    }

    .mobile-nav-links li::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent 0%, rgba(0, 0, 128, 0.1) 50%, transparent 100%);
        transition: left 0.6s ease;
        z-index: 1;
    }

    .mobile-nav-links li:hover::before {
        left: 100%;
    }

    .mobile-nav-links li:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 128, 0.15);
    }

    .mobile-nav-links li a {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem 1rem;
        text-decoration: none;
        color: white;
        /* background-color: navy; */
        margin-bottom: 1rem;
        border-radius: 10px;
        font-size: 1.2rem;
        font-weight: 600;
        transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        position: relative;
        z-index: 2;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }

    .mobile-nav-links li a i {
        font-size: 1.4rem;
        width: 24px;
        text-align: center;
        background: white;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        filter: drop-shadow(0 1px 2px rgba(0, 212, 255, 0.3));
    }

    .mobile-nav-links li a:hover {
        color: #00d4ff;
        transform: translateX(8px);
    }

    .mobile-nav-links li a:hover i {
        transform: scale(1.2) rotate(5deg);
        filter: drop-shadow(0 2px 8px rgba(0, 212, 255, 0.6));
    }



    .mobile-apply-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.8rem;
        margin: 2rem 1.5rem 1.5rem;
        padding: 1.2rem 2.5rem;
        border-radius: 30px;
        background: #687FE5 !important;
        color: white;
        border: none;
        cursor: pointer;
        font-size: 1rem;
        font-weight: 700;
        transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        width: calc(100% - 3rem);
        text-transform: uppercase;
        letter-spacing: 1px;
        position: relative;
        overflow: hidden;
    }

    .mobile-apply-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
        transition: left 0.6s ease;
    }

    .mobile-apply-btn:hover::before {
        left: 100%;
    }

    .mobile-apply-btn i {
        font-size: 1.3rem;
        transition: transform 0.3s ease;
    }

    .mobile-apply-btn:hover {
        background: #5a6fd8;
        transform: translateY(-2px);
    }

    .mobile-apply-btn:hover i {
        transform: translateX(3px) rotate(15deg);
    }

    .mobile-apply-btn:active {
        transform: translateY(-2px) scale(0.98);
        box-shadow: 0 6px 20px rgba(255, 75, 87, 0.4);
    }

/* Countries Image Marquee Section */
.countries-image-marquee-section {
    background: #f8f9fa;
    padding: 60px 0;
    overflow: hidden;
    position: relative;
}

.image-marquee-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    overflow: hidden;
    position: relative;
}

.image-marquee-content {
    display: flex;
    animation: imageMarqueeScroll 40s linear infinite;
    gap: 20px;
    width: max-content;
    transform: translateX(0);
}

.marquee-country-card {
    position: relative;
    width: 250px;
    height: 180px;
    border-radius: 12px;
    overflow: hidden;
    flex-shrink: 0;
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.marquee-country-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.marquee-country-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.marquee-country-card:hover img {
    transform: scale(1.05);
}

.marquee-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 20px;
    text-align: center;
}

.marquee-overlay h4 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 8px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
    line-height: 1.2;
}

.marquee-overlay p {
    font-size: 1.3rem;
    margin: 0;
    opacity: 0.95;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
    font-weight: 500;
}

@keyframes imageMarqueeScroll {
    0% { transform: translateX(0); }
    100% { transform: translateX(-50%); }
}

/* Make Your Choice Section */
.choice-section {
    background: #f8f9fa;
    padding: 80px 0;
}

.choice-container {
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 0;
}

.choice-content {
    text-align: center;
    margin-bottom: 60px;
    padding: 0 20px;
}

.choice-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 20px;
    line-height: 1.2;
}

.choice-text p {
    font-size: 1.1rem;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.countries-marquee-choice {
    width: 100%;
    max-width: 100vw;
    overflow: hidden;
}

.choice-marquee-line {
    overflow: hidden;
    margin-bottom: 20px;
    transform: translateZ(0);
    backface-visibility: hidden;
}

.choice-marquee-content {
    display: flex;
    gap: 15px;
    width: max-content;
}

.choice-marquee-right .choice-marquee-content {
    animation: choiceMarqueeRight 40s linear infinite;
    will-change: transform;
}

.choice-marquee-left .choice-marquee-content {
    animation: choiceMarqueeLeft 40s linear infinite;
    will-change: transform;
}

.country-choice-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px 35px;
    background: white;
    border-radius: 50px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    min-height: 70px;
    flex-shrink: 0;
}

.country-choice-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

.country-choice-item img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.country-choice-item span {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e3a8a;
    white-space: nowrap;
}

@keyframes choiceMarqueeRight {
    0% { transform: translateX(0); }
    100% { transform: translateX(-50%); }
}

@keyframes choiceMarqueeLeft {
    0% { transform: translateX(-50%); }
    100% { transform: translateX(0); }
}

/* Services Section */
.services-section {
    background: #f8f9fa;
    padding: 100px 0;
}

.services-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.services-header {
    text-align: center;
    margin-bottom: 60px;
}

.services-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: navy;
    margin-bottom: 20px;
    line-height: 1.2;
}

.services-header p {
    font-size: 1.1rem;
    color: #6b7280;
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.6;
}

.services-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: stretch;
    margin-top: 50px;
}

.services-left {
    background: white;
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.highlight-badge {
    background: navy;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 25px;
}

.highlight-badge i {
    color: #f59e0b;
    font-size: 1rem;
}

.service-highlight h3 {
    font-size: 2rem;
    font-weight: 700;
    color: navy;
    margin-bottom: 20px;
    line-height: 1.3;
}

.service-highlight p {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 30px;
    font-size: 1rem;
}

.service-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-top: 30px;
}

.stat-item {
    text-align: center;
    padding: 20px 10px;
    background: #f8fafc;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: navy;
    color: white;
    transform: translateY(-3px);
}

.stat-item h4 {
    font-size: 1.8rem;
    font-weight: 700;
    color: navy;
    margin-bottom: 5px;
    transition: color 0.3s ease;
}

.stat-item:hover h4 {
    color: white;
}

.stat-item p {
    font-size: 0.9rem;
    color: #6b7280;
    margin: 0;
    transition: color 0.3s ease;
}

.stat-item:hover p {
    color: rgba(255, 255, 255, 0.9);
}

.service-features {
    margin-top: 30px;
    padding-top: 25px;
    border-top: 1px solid #e5e7eb;
}

.service-features h4 {
    font-size: 1.2rem;
    font-weight: 700;
    color: navy;
    margin-bottom: 20px;
}

.feature-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.feature-point {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
}

.feature-point i {
    color: #10b981;
    background: #d1fae5;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    flex-shrink: 0;
}

.feature-point span {
    color: #374151;
    font-size: 0.95rem;
    font-weight: 500;
}

.contact-cta {
    margin-top: 30px;
    padding-top: 25px;
    border-top: 1px solid #e5e7eb;
    text-align: center;
}

.cta-btn {
    background: navy;
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 139, 0.2);
}

.cta-btn:hover {
    background: #1e40af;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 139, 0.3);
}

.cta-btn i {
    font-size: 1rem;
}

.services-right {
    background: white;
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.services-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 25px;
    max-width: 600px;
    margin: 0 auto;
}

.service-item {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    text-align: left;
    gap: 20px;
    padding: 25px;
    background: #f8fafc;
    border-radius: 15px;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
    min-height: 150px;
    height: 150px;
    overflow: hidden;
}

.service-item:hover {
    background: #e0e7ff;
    border-left-color: navy;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.service-icon-small {
    width: 50px;
    height: 50px;
    background: navy;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.service-item:hover .service-icon-small {
    background: #1e40af;
    transform: scale(1.1);
}

.service-icon-small i {
    font-size: 1.3rem;
    color: white;
}

.service-content {
    flex: 1;
}

.service-content h4 {
    font-size: 1.2rem;
    font-weight: 700;
    color: navy;
    margin-bottom: 10px;
    line-height: 1.3;
}

.service-content p {
    color: #6b7280;
    line-height: 1.4;
    margin: 0;
    font-size: 0.9rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

@media (max-width: 768px) {
    .services-section {
        padding: 60px 0;
    }
    
    .services-header h2 {
        font-size: 2rem;
    }
    
    .services-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .services-left {
        padding: 30px;
    }
    
    .services-right {
        padding: 30px;
    }
    
    .service-highlight h3 {
        font-size: 1.7rem;
    }
    
    .service-stats {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .services-list {
        grid-template-columns: 1fr;
        gap: 20px;
        max-width: 100%;
    }

    .service-item {
        flex-direction: row;
        text-align: left;
        gap: 15px;
        padding: 20px;
        min-height: 120px;
        height: 120px;
        align-items: flex-start;
        overflow: hidden;
    }

    .service-content h4 {
        font-size: 1rem;
        margin-bottom: 8px;
    }

    .service-content p {
        font-size: 0.85rem;
        line-height: 1.3;
        -webkit-line-clamp: 2;
    }

    .service-content p {
        font-size: 0.8rem;
        -webkit-line-clamp: 2;
    }
    
    .service-icon-small {
        margin: 0 auto;
    }
}

/* Visa Assistance Section */
.visa-assistance-section {
    background: white;
    padding: 100px 0;
}

.visa-assistance-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.assistance-title {
    text-align: center;
    margin-bottom: 30px;
}

.assistance-title h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e3a8a;
    line-height: 1.2;
}

.assistance-description {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 60px;
}

.assistance-description p {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.6;
}

.assistance-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.assistance-image img {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.feature-badge {
    background: #1e3a8a;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 20px;
}

.assistance-features h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 20px;
}

.assistance-features p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 15px;
}

.features-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin: 30px 0;
}

.feature-column ul {
    list-style: none;
    padding: 0;
}

.feature-column li {
    color: #666;
    margin-bottom: 10px;
    padding-left: 20px;
    position: relative;
}

.feature-column li::before {
    content: '•';
    color: #1e3a8a;
    font-weight: bold;
    position: absolute;
    left: 0;
}

.assistance-buttons {
    display: flex;
    gap: 20px;
    margin-top: 30px;
}

.btn-know-more {
    background: #1e3a8a;
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-know-more:hover {
    background: #2563eb;
    transform: translateY(-2px);
}

.btn-call-us {
    background: transparent;
    color: #1e3a8a;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    border: 2px solid #1e3a8a;
    transition: all 0.3s ease;
}

.btn-call-us:hover {
    background: #1e3a8a;
    color: white;
    transform: translateY(-2px);
}

/* Office Door Step Section */
.doorstep-section {
    background: #f8f9fa;
    padding: 100px 0;
}

.doorstep-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.doorstep-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

/* Doorstep Image */
.doorstep-image {
    background: transparent;
    width: 100%;
}

.doorstep-image img {
    width: 100%;
    height: auto;
    object-fit: contain;
    object-position: center;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    display: block;
    max-width: 100%;
}

.doorstep-image img:hover {
    transform: scale(1.02);
}

/* Feature Badge */
.feature-badge {
    background: transparent;
    color: #1e3a8a;
    padding: 10px 20px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 20px;
    border: 2px solid #1e3a8a;
    transition: all 0.3s ease;
}

.feature-badge:hover {
    background: #1e3a8a;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(30, 58, 138, 0.3);
}

.feature-badge i {
    font-size: 1rem;
}

.doorstep-features h2 {
    font-size: 2.2rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 20px;
    line-height: 1.2;
}

.doorstep-features p {
    color: #1e3a8a;
    line-height: 1.6;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

/* Features List */
.features-list {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin: 30px 0;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #1e3a8a;
    font-weight: 500;
}

.feature-item i {
    color: #1e3a8a;
    font-size: 1.2rem;
}

.doorstep-buttons {
    display: flex;
    gap: 20px;
    margin-top: 30px;
}

/* What our customers say Section (Like visaguy.ae) */
.customer-testimonial-section {
    background: white;
    padding: 100px 0;
}

.customer-testimonial-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.testimonial-content {
    display: grid;
    grid-template-columns: 1fr 1.5fr;
    gap: 60px;
    align-items: center;
}

.testimonial-image img {
    width: 100%;
    height: auto;
    max-width: 400px;
    object-fit: contain;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.testimonial-text h6 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.testimonial-text {
    width: 100%;
    padding-right: 0;
}

.testimonial-text p {
    font-size: 1.3rem;
    font-weight: 500;
    color: #1e3a8a;
    line-height: 1.7;
    margin-bottom: 30px;
    font-style: italic;
    width: 100%;
    letter-spacing: 0.5px;
}

.customer-info {
    margin-bottom: 25px;
}

.customer-name-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 30px;
    margin-bottom: 5px;
}

.customer-info h5 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #1e3a8a;
    margin: 0;
}

.customer-info .date {
    color: #666;
    font-size: 0.9rem;
}

.read-reviews-link {
    color: #1e3a8a;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    position: relative;
    white-space: nowrap;
}

.read-reviews-link::after {
    content: '→';
    margin-left: 8px;
    width: 24px;
    height: 24px;
    background: #1e3a8a;
    color: white;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.3s ease;
}

.read-reviews-link:hover {
    color: #2563eb;
}

.read-reviews-link:hover::after {
    background: #2563eb;
    transform: translateX(3px) scale(1.1);
}

/* What Sets Us Apart Section (Exact visaguy.ae style) */
.sets-apart-section {
    background: white;
    padding: 100px 0;
}

.sets-apart-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.sets-apart-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: flex-start;
}

.sets-apart-text {
    padding-right: 20px;
}

.feature-badge {
    background: transparent;
    color: #1e3a8a;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 20px;
    border: 2px solid #1e3a8a;
}

.feature-badge i {
    color: #f59e0b;
    font-size: 1rem;
}

.sets-apart-text h2 {
    font-size: 2.2rem;
    font-weight: 700;
    color: navy;
    margin-bottom: 20px;
    line-height: 1.3;
}

.sets-apart-text > p {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 30px;
    font-size: 1rem;
}

.apart-features {
    margin-bottom: 40px;
}

.apart-feature-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 20px;
}

.apart-feature-item i {
    color: #3b82f6;
    background: #dbeafe;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    margin-top: 2px;
    flex-shrink: 0;
}

.apart-feature-item p {
    color: #374151;
    line-height: 1.6;
    margin: 0;
    font-size: 0.95rem;
}

.apart-buttons {
    display: flex;
    gap: 20px;
    margin-top: 30px;
}

.btn-apply-now {
    background: #1e3a8a;
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.btn-apply-now:hover {
    background: #1e40af;
    transform: translateY(-1px);
}

.sets-apart-image {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sets-apart-image img {
    width: 100%;
    height:100%;
    object-fit: cover;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Testimonials Section (Image Style) */
.testimonials-section {
    background: #f8fafc;
    padding: 100px 0;
}

.testimonials-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

.testimonials-header {
    text-align: center;
    margin-bottom: 60px;
}

.testimonial-badge {
    background: transparent;
    color: navy;
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    letter-spacing: 1px;
    display: inline-block;
    margin-bottom: 20px;
}

.testimonials-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: navy;
    margin: 0;
    line-height: 1.2;
}

.testimonials-slider {
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
}

.testimonials-swiper {
    padding: 0 50px 50px 50px;
}

.testimonials-swiper .swiper-slide {
    height: auto;
}

.testimonial-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    border: none;
    transition: all 0.3s ease;
    position: relative;
    min-height: 320px;
    height: auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.quote-icon {
    width: 50px;
    height: 50px;
    min-width: 50px;
    min-height: 50px;
    background: navy;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    flex-shrink: 0;
}

.quote-icon i {
    color: white;
    font-size: 1.5rem;
}

.stars {
    display: flex;
    gap: 5px;
    margin-bottom: 20px;
}

.stars i {
    color: #f59e0b;
    font-size: 1.1rem;
}

.testimonial-card p {
    color: #333333;
    line-height: 1.6;
    font-size: 1.1rem;
    margin-bottom: 10px;
    flex-grow: 1;
    transition: all 0.3s ease;
}

.testimonial-card p.truncated {
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
}

.testimonial-card p.expanded {
    overflow: visible;
    display: block;
    -webkit-line-clamp: unset;
}

.read-more-btn {
    background: none;
    border: none;
    color: navy;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    padding: 0;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    text-decoration: underline;
}

.read-more-btn:hover {
    color: #1e40af;
    text-decoration: none;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: auto;
    flex-shrink: 0;
    padding-top: 10px;
}

.author-info h4 {
    color: #1f2937;
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 4px 0;
}

.author-info span {
    color: #6b7280;
    font-size: 0.9rem;
}

/* Contact Section - eVisa Theme Style */
.contact-section {
    background: #f8fafc;
    padding: 100px 0;
}

.contact-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.contact-header {
    text-align: center;
    margin-bottom: 60px;
}

.contact-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: navy;
    margin-bottom: 15px;
}

.contact-header p {
    font-size: 1.1rem;
    color: #64748b;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1.5fr;
    gap: 0;
    align-items: stretch;
}

.contact-info {
    background: #1E3A79;
    padding: 30px 25px;
    border-radius: 20px 0 0 20px;
    color: white;
    position: relative;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.contact-info::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s linear infinite;
}

@keyframes float {
    0% { transform: translateY(0) rotate(0deg); }
    100% { transform: translateY(-20px) rotate(360deg); }
}

.contact-info-header {
    text-align: center;
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

.contact-info-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 8px;
    color: white;
}

.contact-info-header p {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.4;
}

.info-cards {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
    position: relative;
    z-index: 2;
    flex-grow: 1;
}

.info-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 15px;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    text-align: left;
}

.info-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(10px);
}

.info-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.info-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.info-card:hover .info-icon {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.info-icon i {
    color: white;
    font-size: 1.1rem;
}

.info-text h4 {
    color: white;
    font-size: 0.95rem;
    font-weight: 700;
    margin-bottom: 4px;
}

.info-text p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.8rem;
    line-height: 1.3;
    margin-bottom: 3px;
}

.info-text p:last-child {
    margin-bottom: 0;
}

.contact-cta {
    margin-top: 20px;
    text-align: center;
    position: relative;
    z-index: 2;
}

.cta-button {
    background: white;
    color: navy;
    padding: 10px 20px;
    border-radius: 10px;
    text-decoration: none;
    font-weight: 700;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.cta-button:hover {
    background: #f8fafc;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.cta-button i {
    font-size: 1.1rem;
}

/* Social Media Icons in Contact Info */
.social-icons-list {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    margin-top: 5px;
}

.social-link {
    width: 35px;
    height: 35px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.social-link:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.social-link i {
    color: white;
    font-size: 1.1rem;
}

.social-card .info-text {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* Contact Form - Simple Professional Style */
.contact-form {
    background: white;
    padding: 30px 25px;
    border-radius: 0 20px 20px 0;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.form-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e2e8f0;
}

.form-header h3 {
    color: navy;
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 8px;
}

.form-header p {
    color: #6b7280;
    font-size: 0.9rem;
    line-height: 1.4;
}

.contact-form-wrapper {
    width: 100%;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 12px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    color: #374151;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 6px;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.95rem;
    font-family: 'Poppins', sans-serif;
    transition: all 0.3s ease;
    background: #f9fafb;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: navy;
    background: white;
    box-shadow: 0 0 0 3px rgba(0, 0, 139, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 70px;
    line-height: 1.5;
}

.form-group select {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

.checkbox-group {
    margin-bottom: 18px;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    cursor: pointer;
    font-size: 0.85rem;
    color: #6b7280;
    line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
    position: relative;
    cursor: pointer;
}

.checkbox-label a {
    color: navy;
    text-decoration: none;
    font-weight: 600;
}

.checkbox-label a:hover {
    text-decoration: underline;
}

.submit-btn {
    background: navy;
    color: white;
    padding: 14px 30px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: auto;
    box-shadow: 0 4px 12px rgba(0, 0, 139, 0.2);
}

.submit-btn:hover {
    background: #1e40af;
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 139, 0.3);
}

.submit-btn:active {
    transform: translateY(0);
}

.submit-btn i {
    font-size: 1rem;
}

/* Footer Section - Professional Design */
.footer-section {
    background: url('images/hero/Footer.jpg') center center / cover no-repeat;
    position: relative;
    width: 100%;
    height: auto;
    overflow: hidden;
}

/* Apply Visa CTA Section - Professional */
.apply-visa-cta {
    background: #1E3A79;
    padding: 100px 0;
    text-align: center;
    color: white;
    position: relative;
}

.cta-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

.apply-visa-cta h6 {
    font-size: 0.85rem;
    font-weight: 600;
    margin-bottom: 20px;
    letter-spacing: 3px;
    text-transform: uppercase;
    opacity: 0.95;
    color: #e0e7ff;
}

.apply-visa-cta h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 30px;
    line-height: 1.2;
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.apply-visa-cta p {
    font-size: 1.2rem;
    line-height: 1.7;
    margin-bottom: 50px;
    opacity: 0.95;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    font-weight: 400;
}

.apply-btn {
    background: transparent;
    color: white;
    padding: 18px 45px;
    border-radius: 50px;
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 700;
    letter-spacing: 1.5px;
    text-transform: uppercase;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-block;
    border: 2px solid white;
}

.apply-btn:hover {
    background: white;
    color: #1E3A79;
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
    border: 2px solid white;
}

/* Get In Touch Heading in CTA Section */
.get-in-touch-heading {
    color: white;
    font-size: 2rem;
    font-weight: 700;
    margin-top: 60px;
    margin-bottom: 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
}

/* Main Footer - Professional */
.main-footer {
    background: url('images/hero/Footer.jpg');
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    background-attachment: scroll;
    padding: 80px 0 40px;
    position: relative;
    color: white;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: 1.5fr 1fr 1fr;
    gap: 60px;
    margin-bottom: 50px;
    align-items: start;
}

/* Footer Left - Logo Section */
.footer-left {
    padding-right: 40px;
}

.footer-logo {
    width: 140px;
    height: auto;
    margin-bottom: 30px;
    filter: drop-shadow(3px 3px 8px rgba(0, 0, 0, 0.7));
}

.footer-tagline {
    color: rgba(255, 255, 255, 0.95);
    font-size: 1.1rem;
    margin-bottom: 15px;
    line-height: 1.6;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    font-weight: 400;
}

.footer-slogan {
    color: white;
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 35px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    letter-spacing: 0.5px;
}

.social-links {
    display: flex;
    gap: 15px;
    margin-top: 10px;
}

.social-links a {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.social-links a:hover {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.social-links a i {
    color: white;
    font-size: 1.3rem;
    transition: all 0.3s ease;
}

.social-links a:hover i {
    transform: scale(1.1);
}

/* Footer Middle - Quick Links */
.footer-middle h4 {
    color: white;
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 25px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    letter-spacing: 0.5px;
}

.footer-middle ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.footer-middle ul li {
    display: block;
    width: 100%;
}

.footer-middle ul li a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-size: 1rem;
    transition: all 0.3s ease;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    font-weight: 400;
    display: block;
    padding: 5px 0;
    line-height: 1.5;
}

.footer-middle ul li a:hover {
    color: white;
    padding-left: 8px;
    font-weight: 500;
}

/* Footer Right - Contact Info */
.footer-right h4 {
    color: white;
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 25px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    letter-spacing: 0.5px;
}

.contact-details p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    font-weight: 400;
    line-height: 1.5;
}

.contact-details p strong {
    color: white;
    font-weight: 600;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Copyright - Professional */
.copyright {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding: 35px 0;
    text-align: center;
    margin-top: 50px;
}

.copyright p {
    color: rgba(255, 255, 255, 0.8);
    margin: 6px 0;
    font-size: 0.9rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    font-weight: 400;
    letter-spacing: 0.3px;
}




    ul button:hover{
        background: rgb(1, 1, 180);
        color: white;
    }
    ul li a{
        text-decoration: none;
        font-size: 1.2rem;
        color:navy;
    }
ul li a:hover {
    text-decoration: none;
    font-size: 1.2rem;
    color: rgb(0, 0, 192);
}

/* Professional Dropdown Styles */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-content {
    display: none;
    position: absolute;
    background: white;
    min-width: 320px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    border-radius: 20px;
    padding: 20px 0;
    top: 100%;
    left: 0;
    border: 1px solid rgba(59, 130, 246, 0.1);
    opacity: 0;
    transform: translateY(-15px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
    overflow: hidden;
    margin-top: 5px;
}

.dropdown-content::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 30px;
    width: 16px;
    height: 16px;
    background: white;
    border: 1px solid rgba(59, 130, 246, 0.1);
    border-bottom: none;
    border-right: none;
    transform: rotate(45deg);
    z-index: -1;
}

.dropdown:hover .dropdown-content,
.dropdown-content:hover {
    display: block;
    opacity: 1;
    transform: translateY(0);
    animation: dropdownFadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Ensure dropdown stays open when hovering over the gap */
.dropdown {
    position: relative;
}

.dropdown::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 10px;
    background: transparent;
    z-index: 999;
}

@keyframes dropdownFadeIn {
    0% {
        opacity: 0;
        transform: translateY(-15px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.dropdown-content a {
    color: #1e3a8a !important;
    padding: 18px 25px;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 18px;
    font-size: 1rem !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-bottom: 1px solid rgba(241, 245, 249, 0.8);
    position: relative;
    overflow: hidden;
}

.dropdown-content a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    transform: scaleY(0);
    transition: transform 0.3s ease;
    transform-origin: bottom;
}

.dropdown-content a:last-child {
    border-bottom: none;
}

.dropdown-content a:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
    color: #1e40af !important;
    transform: translateX(8px);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.1);
}

.dropdown-content a:hover::before {
    transform: scaleY(1);
    transform-origin: top;
}

.dropdown-content a i {
    font-size: 1.6rem !important;
    color: #3b82f6;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(96, 165, 250, 0.1));
    border-radius: 12px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
}

.dropdown-content a:hover i {
    color: #1e40af;
    transform: scale(1.1) rotate(5deg);
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(96, 165, 250, 0.2));
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
}

.dropdown-content a div {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;
}

.dropdown-content a strong {
    font-weight: 700;
    font-size: 1.1rem;
    color: #1e3a8a;
    line-height: 1.3;
    transition: all 0.3s ease;
}

.dropdown-content a span {
    font-size: 0.9rem;
    color: #6b7280;
    font-weight: 500;
    line-height: 1.4;
    transition: all 0.3s ease;
}

.dropdown-content a:hover strong {
    color: #1e40af;
    transform: translateX(2px);
}

.dropdown-content a:hover span {
    color: #4b5563;
    transform: translateX(2px);
}

.dropdown > a i {
    font-size: 0.9rem;
    margin-left: 8px;
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown:hover > a i,
.dropdown.active > a i {
    transform: rotate(180deg);
}

/* Active state for clicked dropdown */
.dropdown.active .dropdown-content {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

/* Mobile Dropdown Styles */
@media (max-width: 768px) {
    .dropdown-content {
        min-width: 280px;
        padding: 15px 0;
        border-radius: 15px;
        margin-top: 8px;
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
    }

    .dropdown-content::before {
        left: 20px;
        width: 12px;
        height: 12px;
        top: -6px;
    }

    .dropdown-content a {
        padding: 15px 20px;
        gap: 15px;
    }

    .dropdown-content a i {
        width: 40px;
        height: 40px;
        font-size: 1.4rem !important;
        border-radius: 10px;
    }

    .dropdown-content a strong {
        font-size: 1rem;
    }

    .dropdown-content a span {
        font-size: 0.85rem;
    }

    .dropdown-content a:hover {
        transform: translateX(5px);
    }
}
.hero-container{
    width: 80%;
    height: 65vh;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: translate(-50%, -50%);
    left: 50%;
    top: 50%;
    position: absolute;
    z-index: 1;
    gap: 5rem;
}

.hero-text{
    width: 50%;
    height: 100%;
    color: navy;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding:0 0 0 12rem;
    margin-top: 3rem;
}
.hero-container>.slider {
    width: 40%;
    height: 75%;
    color: white;
    background: transparent;
    position: relative;
    overflow: hidden;
    margin-top: 3rem;
    margin-left: auto;
    margin-right: auto;
}
.hero-text h1{
    font-size: 4rem;
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
    width: 80%;
}
.hero-text p {
    font-size: 1.2rem;
    font-weight: 100;
    line-height: 1.2;
    margin-bottom: 1rem;
    width: 90%;
}
.hero-text button{
    padding: 1rem 2rem;
    border-radius: 50px;
    background-color: navy;
    color: white;
    cursor: pointer;
    font-size: 1.2rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    width: 60%;
    margin-top: 1rem;
    margin-bottom: 2rem;
}
.hero-text button:hover{
    background: rgb(1, 1, 180);
    color: white;
}

.swiper {
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
    position: relative;
}

.swiper-wrapper {
    transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.swiper-slide {
    border-radius: 20px;
    overflow: hidden;
    position: relative;
}

.swiper-slide img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.slide-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: white;
    padding: 2rem 1.5rem 1.5rem;
    text-align: left;
    transition: all 0.6s ease;
    opacity: 1;
    transform: translateY(0);
}

.slide-content h3 {
    font-size: 3rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    color: white;
}

.slide-content p {
    font-size: 1.6rem;
    margin: 0;
    opacity: 0.9;
    color: white;
}
/* Hero Slider Navigation Arrows */
.swiper-button-next,
.swiper-button-prev {
    width: 20px;
    height: 20px;
    margin-top: -10px;
}

.swiper-button-next:after,
.swiper-button-prev:after {
    font-size: 10px;
    font-weight: 700;
}

.slider .arrow{
    color: lightblue;
    font-size: 10px;

}

/* Tablet Styles */
@media screen and (max-width: 1024px) {
    .hero-container {
        width: 90%;
        flex-direction: column;
        height: auto;
        transform: none;
        left: auto;
        top: auto;
        margin: 8vh auto 0;
        padding: 2rem 0;
    }

    .hero-text {
        width: 100%;
        padding: 2rem;
        text-align: center;
        margin-bottom: 2rem;
    }

    .hero-text h1 {
        font-size: 3.5rem;
        width: 100%;
        text-align: center;
    }

    .hero-text p {
        width: 100%;
        font-size: 1.4rem;
        text-align: center;
    }

    .hero-text button {
        width: 80%;
        margin: 1rem auto;
    }

    .hero-container>.slider {
        width: 80%;
        height: 60vh;
    }

    .slide-content h3 {
        font-size: 3rem;
    }

    .slide-content p {
        font-size: 1.8rem;
    }
}

/* Mobile Responsive for Sets Apart Section */
@media (max-width: 768px) {
    .sets-apart-section {
        padding: 60px 0;
    }

    .sets-apart-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .sets-apart-text {
        padding-right: 0;
        order: 2;
    }

    .sets-apart-image {
        order: 1;
    }

    .sets-apart-text h2 {
        font-size: 1.8rem;
    }

    .apart-buttons {
        flex-direction: column;
        gap: 15px;
    }

    .btn-apply-now {
        text-align: center;
        width: 100%;
    }

    /* Testimonials Mobile */
    .testimonials-section {
        padding: 60px 0;
    }

    .testimonials-header h2 {
        font-size: 2rem;
    }

    .testimonials-swiper {
        padding: 0 20px 50px 20px;
    }



    .testimonial-card {
        padding: 15px;
        margin: 0 5px;
        min-height: 250px;
        height: auto;
    }

    .testimonial-card p {
        font-size: 1rem;
        line-height: 1.6;
        margin-bottom: 20px;
        color: #333333;
    }

    .quote-icon {
        width: 35px;
        height: 35px;
        min-width: 35px;
        min-height: 35px;
        margin-bottom: 15px;
        background: navy;
        flex-shrink: 0;
    }

    .quote-icon i {
        font-size: 1rem;
    }

    .stars {
        margin-bottom: 15px;
    }

    .stars i {
        font-size: 1rem;
    }



    .author-info h4 {
        font-size: 0.9rem;
    }

    .author-info span {
        font-size: 0.8rem;
    }

    /* Contact Section Mobile - eVisa Style */
    .contact-section {
        padding: 60px 0;
    }

    .contact-header h2 {
        font-size: 2rem;
    }

    .contact-header p {
        font-size: 1rem;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 0;
    }

    .contact-info {
        order: 2;
        padding: 40px 30px;
        border-radius: 0 0 20px 20px;
    }

    .contact-info-header h3 {
        font-size: 1.8rem;
    }

    .contact-info-header p {
        font-size: 1rem;
    }

    .info-cards {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .info-card {
        padding: 20px;
    }

    .info-item {
        gap: 12px;
    }

    .info-icon {
        width: 45px;
        height: 45px;
    }

    .info-icon i {
        font-size: 1.2rem;
    }

    .info-text h4 {
        font-size: 1rem;
        margin-bottom: 6px;
    }

    .info-text p {
        font-size: 0.9rem;
    }

    .contact-cta {
        margin-top: 30px;
    }

    .cta-button {
        padding: 12px 25px;
        font-size: 0.95rem;
    }

    .social-icons-list {
        gap: 10px;
        justify-content: flex-start;
    }

    .social-link {
        width: 32px;
        height: 32px;
    }

    .social-link i {
        font-size: 1rem;
    }

    .contact-form {
        order: 1;
        padding: 40px 30px;
        border-radius: 20px 20px 0 0;
    }

    .form-header h3 {
        font-size: 1.6rem;
    }

    .form-header p {
        font-size: 0.95rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .form-group input,
    .form-group textarea,
    .form-group select {
        padding: 12px 15px;
    }

    .checkbox-label {
        font-size: 0.85rem;
    }

    .submit-btn {
        padding: 14px 25px;
        font-size: 0.95rem;
    }

    /* Footer Mobile - Visaguy Style */
    .apply-cta-section {
        padding: 60px 0;
    }

    .apply-cta-content h6 {
        font-size: 0.9rem;
    }

    .apply-cta-content h2 {
        font-size: 2rem;
        letter-spacing: 1px;
    }

    .apply-cta-content > p {
        font-size: 1.2rem;
    }

    .apply-visa-cta p {
        font-size: 1rem;
        margin-bottom: 30px;
    }

    .apply-btn {
        padding: 12px 25px;
        font-size: 0.9rem;
    }

    .main-footer {
        padding: 60px 0 30px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 50px;
        text-align: center;
    }

    .footer-left {
        padding-right: 0;
        order: 1;
    }

    .footer-middle {
        order: 2;
    }

    .footer-right {
        order: 3;
    }

    .footer-logo {
        width: 120px;
        margin: 0 auto 25px;
    }

    .footer-tagline {
        font-size: 1.1rem;
        margin-bottom: 12px;
    }

    .footer-slogan {
        font-size: 1.2rem;
        margin-bottom: 25px;
    }

    .social-links {
        justify-content: center;
        gap: 20px;
    }

    .footer-middle h4,
    .footer-right h4 {
        font-size: 1.2rem;
        margin-bottom: 20px;
    }

    .footer-middle ul {
        gap: 10px;
    }

    .footer-middle ul li a {
        font-size: 1.1rem;
        padding: 8px 0;
    }

    .contact-details p {
        justify-content: center;
        font-size: 1.1rem;
        margin-bottom: 18px;
    }

    .copyright {
        padding: 30px 0;
        margin-top: 40px;
    }

    .copyright p {
        font-size: 0.95rem;
        margin: 8px 0;
    }
}

/* Mobile Styles */
@media screen and (max-width: 768px) {
    nav {
        height: 8vh;
        padding: 0 4%;
    }

    nav img {
        height: 45px;
    }

    .desktop-menu {
        display: none;
    }

    .mobile-menu-btn {
        display: block;
    }

    .mobile-menu {
        display: block;
    }

    .hero {
        min-height: 100vh;
        padding-bottom: 2rem;
    }

    .hero-container {
        width: 95%;
        flex-direction: column;
        height: auto;
        transform: none;
        left: auto;
        top: auto;
        position: static;
        margin: 8vh auto 0;
        padding: 1rem 0.5rem;
        gap: 1rem;
    }

    .hero-text {
        width: 100%;
        padding: 1rem 0.5rem;
        text-align: center;
        order: 2;
    }

    .hero-text h1 {
        font-size: 2.8rem;
        width: 100%;
        line-height: 1.2;
        text-align: center;
        margin-bottom: 1rem;
        white-space: normal;
    }

    .hero-text p {
        width: 100%;
        font-size: 1.2rem;
        line-height: 1.5;
        text-align: center;
        margin-bottom: 1.5rem;
    }

    .hero-text button {
        width: 95%;
        max-width: 350px;
        font-size: 1.1rem;
        padding: 1rem 2rem;
        margin: 0 auto 2rem auto;
        border: 2px solid navy;
        background: navy !important;
        color: white !important;
    }

    .hero-text button:hover {
        background: rgb(1, 1, 180) !important;
        color: white !important;
    }

    .hero-container>.slider {
        width: 100%;
        height: 45vh;
        margin: 0 auto 2rem auto;
        order: 1;
    }

    .slide-content {
        padding: 1.2rem 1rem 1rem;
    }

    .slide-content h3 {
        font-size: 2.5rem;
        margin-bottom: 0.5rem;
    }

    .slide-content p {
        font-size: 1.5rem;
    }
}

/* Small Mobile Styles */
@media screen and (max-width: 480px) {
    nav {
        height: 7vh;
        padding: 0 3%;
    }

    nav img {
        height: 35px;
    }

    .hero {
        background-position: center center;
        min-height: 100vh;
    }

    .hero-container {
        margin: 6vh auto 0;
        padding: 0.5rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .hero-text {
        padding: 0.5rem;
    }

    .hero-text h1 {
        font-size: 2.2rem;
        margin-bottom: 0.8rem;
        line-height: 1.1;
        white-space: normal;
    }

    .hero-text p {
        font-size: 1.1rem;
        margin-bottom: 1rem;
        line-height: 1.4;
    }

    .hero-text button {
        font-size: 1rem;
        padding: 0.9rem 1.5rem;
        width: 98%;
        max-width: 320px;
        margin: 0 auto 1.5rem auto;
    }

    .hero-container>.slider {
        height: 35vh;
        margin: 0 auto 1.5rem auto;
    }

    .slide-content {
        padding: 0.8rem 0.6rem 0.6rem;
    }

    .slide-content h3 {
        font-size: 2rem;
        margin-bottom: 0.3rem;
    }

    .slide-content p {
        font-size: 1.3rem;
    }

    .swiper-button-next,
    .swiper-button-prev {
        width: 18px;
        height: 18px;
    }

    .swiper-button-next:after,
    .swiper-button-prev:after {
        font-size: 8px;
    }

    /* Marquee Mobile Styles */
    .marquee-country-card {
        width: 200px;
        height: 140px;
    }

    .marquee-overlay h4 {
        font-size: 1.3rem;
    }

    .marquee-overlay p {
        font-size: 1rem;
    }

    /* Choice Section Mobile */
    .choice-text h2 {
        font-size: 2rem;
        margin-bottom: 15px;
    }

    .choice-text p {
        font-size: 1rem;
        padding: 0 20px;
    }

    .country-choice-item {
        padding: 15px 25px;
        gap: 15px;
        min-height: 60px;
    }

    .country-choice-item img {
        width: 40px;
        height: 40px;
    }

    .country-choice-item span {
        font-size: 1.2rem;
    }

    /* Visa Assistance Section Mobile */
    .visa-assistance-section {
        padding: 60px 0;
    }

    .assistance-title h2 {
        font-size: 2rem;
        margin-bottom: 20px;
    }

    .assistance-description {
        margin-bottom: 40px;
        padding: 0 20px;
    }

    .assistance-description p {
        font-size: 1rem;
    }

    .assistance-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .assistance-image {
        order: 1;
    }

    .assistance-features {
        order: 2;
    }

    .assistance-features h3 {
        font-size: 1.8rem;
        margin-bottom: 15px;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        margin: 20px 0;
    }

    .assistance-buttons {
        flex-direction: column;
        gap: 15px;
        align-items: center;
    }

    .btn-know-more,
    .btn-call-us {
        width: 100%;
        max-width: 250px;
        text-align: center;
        padding: 12px 20px;
        font-size: 0.9rem;
    }

    /* Doorstep Section Mobile */
    .doorstep-section {
        padding: 60px 0;
    }

    .doorstep-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
        display: flex;
        flex-direction: column;
    }

    .doorstep-image {
        order: -1;
        background: transparent !important;
        padding: 0;
        margin-bottom: 20px;
        width: 100%;
    }

    .doorstep-features {
        order: 1;
    }

    .doorstep-image img {
        height: auto;
        max-height: none;
        object-fit: contain;
        object-position: top center;
        width: 100%;
        border-radius: 15px;
    }

    .doorstep-features h2 {
        font-size: 1.8rem;
        margin-bottom: 15px;
    }

    .doorstep-features p {
        font-size: 1rem;
    }

    .features-list {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .doorstep-buttons {
        flex-direction: column;
        gap: 15px;
        align-items: center;
    }

    /* Customer Testimonial Section Mobile */
    .customer-testimonial-section {
        padding: 60px 0;
    }

    .testimonial-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .testimonial-image {
        order: 1;
    }

    .testimonial-text {
        order: 2;
    }

    .testimonial-image img {
        max-width: 300px;
    }

    .testimonial-text h6 {
        font-size: 0.8rem;
        margin-bottom: 15px;
    }

    .testimonial-text p {
        font-size: 1.5rem;
        font-weight: 300;
        margin-bottom: 25px;
        max-width: 100%;
        letter-spacing: 0.3px;
    }

    .customer-name-row {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        gap: 15px;
    }

    .customer-info h5 {
        font-size: 1.2rem;
        flex: 1;
    }

    .read-reviews-link {
        font-size: 0.8rem;
        flex-shrink: 0;
    }

    .read-reviews-link::after {
        width: 20px;
        height: 20px;
        font-size: 10px;
        margin-left: 6px;
    }

    /* Sets Apart Section Mobile (Like visaguy.ae) */
    .sets-apart-section {
        padding: 80px 0;
    }

    .sets-apart-content {
        grid-template-columns: 1fr;
        gap: 50px;
        text-align: center;
    }

    .sets-apart-text {
        order: 2;
        padding: 40px 30px;
        margin: 0 10px;
    }

    .sets-apart-image {
        order: 1;
        margin: 0 20px;
    }

    .sets-apart-image img {
        height: 300px;
    }

    .sets-apart-text h2 {
        font-size: 2rem;
        margin-bottom: 20px;
    }

    .sets-apart-text > p {
        font-size: 1.05rem;
        margin-bottom: 30px;
    }

    .apart-feature-item {
        text-align: left;
        margin-bottom: 20px;
        padding: 12px 0;
    }

    .apart-feature-item p {
        font-size: 0.95rem;
    }

    .apart-buttons {
        justify-content: center;
    }

    .btn-apply-now {
        padding: 12px 25px;
        font-size: 0.95rem;
    }
}

/* Extra Small Mobile Styles */
@media screen and (max-width: 360px) {
    nav {
        height: 6vh;
        padding: 0 2%;
    }

    nav img {
        height: 30px;
    }

    .hero-container {
        margin: 4vh auto 0;
        padding: 0.3rem;
        width: 98%;
    }

    .hero-text {
        padding: 0.3rem;
    }

    .hero-text h1 {
        font-size: 1.5rem;
        margin-bottom: 0.6rem;
        line-height: 1.1;
    }

    .hero-text p {
        font-size: 0.8rem;
        margin-bottom: 0.8rem;
        line-height: 1.3;
    }

    .hero-text button {
        font-size: 0.8rem;
        padding: 0.6rem 1rem;
        width: 100%;
        max-width: 250px;
    }

    .hero-container>.slider {
        height: 30vh;
        margin: 0 auto 1rem auto;
    }

    .slide-content {
        padding: 0.6rem 0.4rem 0.4rem;
    }

    .slide-content h3 {
        font-size: 1.3rem;
        margin-bottom: 0.2rem;
    }

    .slide-content p {
        font-size: 0.9rem;
    }

    /* Marquee Extra Small Mobile */
    .marquee-country-card {
        width: 150px;
        height: 110px;
    }

    .marquee-overlay h4 {
        font-size: 1.1rem;
    }

    .marquee-overlay p {
        font-size: 0.9rem;
    }

    /* Choice Section Small Mobile */
    .choice-section {
        padding: 60px 0;
    }

    .choice-text h2 {
        font-size: 1.8rem;
        margin-bottom: 12px;
    }

    .choice-text p {
        font-size: 0.9rem;
        padding: 0 15px;
    }

    .country-choice-item {
        padding: 12px 20px;
        gap: 12px;
        min-height: 50px;
    }

    .country-choice-item img {
        width: 35px;
        height: 35px;
    }

    .country-choice-item span {
        font-size: 1rem;
    }

    /* Visa Assistance Section Small Mobile */
    .visa-assistance-section {
        padding: 40px 0;
    }

    .assistance-title h2 {
        font-size: 1.6rem;
        margin-bottom: 15px;
    }

    .assistance-description {
        margin-bottom: 30px;
        padding: 0 15px;
    }

    .assistance-description p {
        font-size: 0.9rem;
    }

    .assistance-content {
        gap: 30px;
        padding: 0 15px;
    }

    .assistance-features h3 {
        font-size: 1.5rem;
        margin-bottom: 12px;
    }

    .assistance-features p {
        font-size: 0.9rem;
        margin-bottom: 12px;
    }

    .feature-column li {
        font-size: 0.85rem;
        margin-bottom: 8px;
        padding-left: 15px;
    }

    .btn-know-more,
    .btn-call-us {
        padding: 10px 18px;
        font-size: 0.85rem;
        max-width: 220px;
    }

    /* Sets Apart Section Small Mobile - Enhanced */
    .sets-apart-section {
        padding: 60px 0;
    }

    .sets-apart-content {
        gap: 40px;
        padding: 0 15px;
    }

    .sets-apart-text {
        padding: 30px 20px;
        margin: 0 5px;
    }

    .sets-apart-image {
        margin: 0 15px;
    }

    .sets-apart-image::before {
        top: -10px;
        left: -10px;
        right: 10px;
        bottom: 10px;
    }

    .sets-apart-text h2 {
        font-size: 1.7rem;
        margin-bottom: 15px;
    }

    .sets-apart-text > p {
        font-size: 1rem;
        margin-bottom: 25px;
    }

    .apart-feature-item {
        margin-bottom: 20px;
        gap: 15px;
        padding: 15px;
    }

    .apart-feature-item p {
        font-size: 0.95rem;
    }

    .apart-feature-item i {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    .btn-apply-now {
        padding: 12px 24px;
        font-size: 0.95rem;
    }

    /* Doorstep Section Small Mobile */
    .doorstep-section {
        padding: 40px 0;
    }

    .doorstep-content {
        gap: 30px;
        padding: 0 15px;
        display: flex;
        flex-direction: column;
    }

    .doorstep-image {
        order: -1;
        background: transparent !important;
        padding: 0;
        margin-bottom: 15px;
        width: 100%;
    }

    .doorstep-features {
        order: 1;
    }

    .doorstep-image img {
        height: auto;
        max-height: none;
        object-fit: contain;
        object-position: top center;
        width: 100%;
        border-radius: 15px;
    }

    .doorstep-features h2 {
        font-size: 1.5rem;
        margin-bottom: 12px;
    }

    .doorstep-features p {
        font-size: 0.9rem;
        margin-bottom: 12px;
    }

    .feature-item {
        gap: 10px;
    }

    .feature-item span {
        font-size: 0.9rem;
    }

    /* Customer Testimonial Section Small Mobile */
    .customer-testimonial-section {
        padding: 40px 0;
    }

    .testimonial-content {
        gap: 30px;
        padding: 0 15px;
    }

    .testimonial-image img {
        max-width: 250px;
    }

    .testimonial-text h6 {
        font-size: 0.9rem;
        margin-bottom: 12px;
    }

    .testimonial-text p {
        font-size: 1rem;
        margin-bottom: 20px;
    }

    .customer-info h5 {
        font-size: 1.1rem;
    }

    .customer-info .date {
        font-size: 0.8rem;
    }

    .read-reviews-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}

/* About Page Professional Styles */

/* About Hero Section */
.about-hero-section {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    padding: 120px 0 80px 0;
    color: white;
    text-align: center;
}

.about-hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.about-hero-content .section-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
}

.about-hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
}

.about-hero-content .hero-subtitle {
    font-size: 1.3rem;
    font-weight: 300;
    opacity: 0.9;
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Main About Section */
.about-main-section {
    background: white;
    padding: 100px 0;
}

.about-main-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.about-main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-intro .intro-text {
    font-size: 1.2rem;
    line-height: 1.8;
    color: #4b5563;
    margin-bottom: 25px;
}

.about-image-content img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Why Choose Section */
.why-choose-section {
    background: #f8fafc;
    padding: 100px 0;
}

.why-choose-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 15px;
}

.section-header p {
    font-size: 1.1rem;
    color: #6b7280;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.feature-card {
    background: white;
    padding: 40px 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.feature-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px auto;
}

.feature-icon i {
    font-size: 2rem;
    color: white;
}

.feature-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 15px;
}

.feature-card p {
    font-size: 1rem;
    line-height: 1.6;
    color: #6b7280;
}

/* Mission Section */
.mission-section {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    padding: 100px 0;
    color: white;
    text-align: center;
}

.mission-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

.mission-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 30px;
}

.mission-text {
    font-size: 1.2rem;
    line-height: 1.8;
    margin-bottom: 40px;
    opacity: 0.9;
}

.mission-cta {
    background: white;
    color: #1e3a8a;
    padding: 15px 40px;
    border-radius: 30px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    font-size: 1.1rem;
}

.mission-cta:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Stats Section Professional Styling */
.stats-section {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    padding: 100px 0;
    color: white;
    position: relative;
    overflow: hidden;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s linear infinite;
}

.stats-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.stats-header {
    text-align: center;
    margin-bottom: 60px;
}

.stats-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.stats-header p {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
}

.stat-item:hover::before {
    opacity: 1;
    transform: rotate(45deg) translate(50%, 50%);
}

.stat-item:hover {
    transform: translateY(-10px);
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.stat-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px auto;
    transition: all 0.3s ease;
}

.stat-item:hover .stat-icon {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1) rotate(360deg);
}

.stat-icon i {
    font-size: 2.5rem;
    color: white;
    transition: all 0.3s ease;
}

.stat-number {
    font-size: 3.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.stat-item:hover .stat-number {
    transform: scale(1.1);
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
}

.stat-label {
    font-size: 1.3rem;
    font-weight: 600;
    color: white;
    margin-bottom: 8px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.stat-description {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
    font-weight: 400;
}

/* About Page Mobile Responsive */
@media (max-width: 768px) {
    .about-hero-section {
        padding: 80px 0 60px 0;
    }

    .about-hero-content h1 {
        font-size: 2.5rem;
    }

    .about-hero-content .hero-subtitle {
        font-size: 1.1rem;
    }

    .about-main-section {
        padding: 60px 0;
    }

    .about-main-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .about-intro .intro-text {
        font-size: 1.1rem;
    }

    .why-choose-section {
        padding: 60px 0;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .feature-card {
        padding: 30px 20px;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .mission-section {
        padding: 60px 0;
    }

    .mission-content h2 {
        font-size: 2rem;
    }

    .mission-text {
        font-size: 1.1rem;
    }

    /* Stats Section Mobile */
    .stats-section {
        padding: 60px 0;
    }

    .stats-header h2 {
        font-size: 2rem;
    }

    .stats-header p {
        font-size: 1rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
        margin-top: 40px;
    }

    .stat-item {
        padding: 30px 20px;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 20px;
    }

    .stat-icon i {
        font-size: 2rem;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .stat-label {
        font-size: 1.1rem;
    }

    .stat-description {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .stat-item {
        padding: 25px 15px;
    }
}

/* Contact Page Professional Styles */

/* Contact Hero Section */
.contact-hero-section {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    padding: 120px 0 80px 0;
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.contact-hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s linear infinite;
}

.contact-hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.hero-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
}

.contact-hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.3rem;
    font-weight: 300;
    opacity: 0.9;
    max-width: 800px;
    margin: 0 auto 40px auto;
    line-height: 1.6;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 60px;
    margin-top: 50px;
}

.hero-stat {
    text-align: center;
}

.hero-stat .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 5px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-stat .stat-label {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

/* Quick Contact Bar */
.quick-contact-bar {
    background: white;
    padding: 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 10;
    margin-top: -30px;
}

.quick-contact-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.quick-contact-items {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0;
}

.quick-contact-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 30px 25px;
    text-decoration: none;
    color: #1e3a8a;
    transition: all 0.3s ease;
    border-right: 1px solid #e5e7eb;
    position: relative;
    overflow: hidden;
}

.quick-contact-item:last-child {
    border-right: none;
}

.quick-contact-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(30, 58, 138, 0.05), transparent);
    transition: left 0.5s ease;
}

.quick-contact-item:hover::before {
    left: 100%;
}

.quick-contact-item:hover {
    background: #f8fafc;
    transform: translateY(-2px);
}

.quick-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.quick-contact-item:hover .quick-icon {
    transform: scale(1.1) rotate(360deg);
}

.quick-icon i {
    font-size: 1.5rem;
    color: white;
}

.quick-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.quick-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #6b7280;
}
    line-height: 1.6;
    color: #6b7280;
}

/* Mission Section */
.mission-section {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    padding: 100px 0;
    color: white;
    text-align: center;
}

.mission-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

.mission-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 30px;
}

.mission-text {
    font-size: 1.2rem;
    line-height: 1.8;
    margin-bottom: 40px;
    opacity: 0.9;
}

.mission-cta {
    background: white;
    color: #1e3a8a;
    padding: 15px 40px;
    border-radius: 30px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.mission-cta:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Stats Section Professional Styling */
.stats-section {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    padding: 100px 0;
    color: white;
    position: relative;
    overflow: hidden;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s linear infinite;
}

.stats-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.stats-header {
    text-align: center;
    margin-bottom: 60px;
}

.stats-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.stats-header p {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: navy;
    color: white;
    transform: translateY(-3px);
}

.stat-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
}

.stat-icon i {
    font-size: 2.5rem;
    color: white;
}

.stat-content {
    text-align: center;
}

.stat-number {
    font-size: 3.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-item:hover .stat-number {
    transform: scale(1.1);
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
}

.stat-label {
    font-size: 1.3rem;
    font-weight: 600;
    color: white;
    margin-bottom: 8px;
}

.stat-description {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
    font-weight: 400;
}

/* About Page Professional Styles */

/* About Hero Section */
.about-hero-section {
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    padding: 120px 0 80px 0;
    color: white;
    text-align: center;
}

.about-hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.about-hero-content .section-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
}

.about-hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.about-hero-content .hero-subtitle {
    font-size: 1.3rem;
    font-weight: 300;
    opacity: 0.9;
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Main About Section */
.about-main-section {
    background: white;
    padding: 100px 0;
}

.about-main-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.about-main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-intro .intro-text {
    font-size: 1.2rem;
    line-height: 1.8;
    color: #4b5563;
    margin-bottom: 25px;
}

.about-image-content img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Why Choose Section */
.why-choose-section {
    background: #f8fafc;
    padding: 100px 0;
}

.why-choose-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 15px;
}

.section-header p {
    font-size: 1.1rem;
    color: #6b7280;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.feature-card {
    background: white;
    padding: 40px 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    text-align: center;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.feature-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px auto;
}

.feature-icon i {
    font-size: 2rem;
    color: white;
}

.feature-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 15px;
}

.feature-card p {
    font-size: 1rem;
    line-height: 1.6;
    color: #6b7280;
}

/* Mission Section */
.mission-section {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    padding: 100px 0;
    color: white;
    text-align: center;
}

.mission-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

.mission-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 30px;
}

.mission-text {
    font-size: 1.2rem;
    line-height: 1.8;
    margin-bottom: 40px;
    opacity: 0.9;
}

.mission-cta {
    background: white;
    color: #1e3a8a;
    padding: 15px 40px;
    border-radius: 30px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.mission-cta:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Stats Section Professional Styling */
.stats-section {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    padding: 100px 0;
    color: white;
    position: relative;
    overflow: hidden;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s linear infinite;
}

.stats-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.stats-header {
    text-align: center;
    margin-bottom: 60px;
}

.stats-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.stats-header p {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: navy;
    color: white;
    transform: translateY(-3px);
}

.stat-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
}

.stat-icon i {
    font-size: 2.5rem;
    color: white;
}

.stat-content {
    text-align: center;
}

.stat-number {
    font-size: 3.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-item:hover .stat-number {
    transform: scale(1.1);
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
}

.stat-label {
    font-size: 1.3rem;
    font-weight: 600;
    color: white;
    margin-bottom: 8px;
}

.stat-description {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
    font-weight: 400;
}

/* About Page Professional Styles */

/* About Hero Section */
.about-hero-section {
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    padding: 120px 0 80px 0;
    color: white;
    text-align: center;
}

.about-hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.about-hero-content .section-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
}

.about-hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.about-hero-content .hero-subtitle {
    font-size: 1.3rem;
    font-weight: 300;
    opacity: 0.9;
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Main About Section */
.about-main-section {
    background: white;
    padding: 100px 0;
}

.about-main-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.about-main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-intro .intro-text {
    font-size: 1.2rem;
    line-height: 1.8;
    color: #4b5563;
    margin-bottom: 25px;
}

.about-image-content img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Why Choose Section */
.why-choose-section {
    background: #f8fafc;
    padding: 100px 0;
}

.why-choose-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 15px;
}

.section-header p {
    font-size: 1.1rem;
    color: #6b7280;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.feature-card {
    background: white;
    padding: 40px 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    text-align: center;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.feature-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px auto;
}

.feature-icon i {
    font-size: 2rem;
    color: white;
}

.feature-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 15px;
}

.feature-card p {
    font-size: 1rem;
    line-height: 1.6;
    color: #6b7280;
}

/* Mission Section */
.mission-section {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    padding: 100px 0;
    color: white;
    text-align: center;
}

.mission-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

.mission-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 30px;
}

.mission-text {
    font-size: 1.2rem;
    line-height: 1.8;
    margin-bottom: 40px;
    opacity: 0.9;
}

.mission-cta {
    background: white;
    color: #1e3a8a;
    padding: 15px 40px;
    border-radius: 30px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.mission-cta:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Stats Section Professional Styling */
.stats-section {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    padding: 100px 0;
    color: white;
    position: relative;
    overflow: hidden;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s linear infinite;
}

.stats-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.stats-header {
    text-align: center;
    margin-bottom: 60px;
}

.stats-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.stats-header p {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: navy;
    color: white;
    transform: translateY(-3px);
}

.stat-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
}

.stat-icon i {
    font-size: 2.5rem;
    color: white;
}

.stat-content {
    text-align: center;
}

.stat-number {
    font-size: 3.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-item:hover .stat-number {
    transform: scale(1.1);
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
}

.stat-label {
    font-size: 1.3rem;
    font-weight: 600;
    color: white;
    margin-bottom: 8px;
}

.stat-description {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
    font-weight: 400;
}

/* About Page Professional Styles */

/* About Hero Section */
.about-hero-section {
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    padding: 120px 0 80px 0;
    color: white;
    text-align: center;
}

.about-hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.about-hero-content .section-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 30px;
    backdrop-filter: blur(10px);

.card-icon i {
    font-size: 1.3rem;
    color: white;
}

.card-content h4 {
    font-size: 1rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 5px;
}

.card-content p {
    font-size: 0.9rem;
    color: #6b7280;
    margin: 0;
    font-weight: 500;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 20px;
    margin: 40px 0;
    padding: 20px;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 20px 15px;
    text-align: center;
    transition: all 0.3s ease;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.stat-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-3px);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 5px;
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.hero-cta-buttons {
    display: flex;
    gap: 20px;
    align-items: center;
}

.primary-cta {
    background: white;
    color: #1e3a8a;
    padding: 15px 30px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.primary-cta:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.secondary-cta {
    background: transparent;
    color: white;
    padding: 15px 25px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.secondary-cta:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: white;
    transform: translateY(-2px);
}

/* Quick Assessment Form */
.quick-assessment-section {
    background: white;
    padding: 60px 0;
    margin-top: 0;
    position: relative;
    z-index: 10;
}

.assessment-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.assessment-header {
    text-align: center;
    margin-bottom: 40px;
}

.assessment-header h3 {
    font-size: 2.2rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 15px;
}

.assessment-header p {
    font-size: 1.1rem;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 20px;
    background: white;
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group input,
.form-group select {
    padding: 15px;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.assessment-submit {
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    color: white;
    padding: 15px 25px;
    border: none;
    border-radius: 10px;
    font-weight: 700;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    height: fit-content;
    align-self: end;
}

.assessment-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(30, 58, 138, 0.3);
}

.hero-cta {
    background: white;
    color: #1e3a8a;
    padding: 15px 40px;
    border-radius: 30px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    font-size: 1.1rem;
    margin-top: 20px;
}

.hero-cta:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Study Countries Section */
.study-countries-section {
    background: #f8fafc;
    padding: 100px 0;
}

.study-countries-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 15px;
}

.section-header p {
    font-size: 1.1rem;
    color: #6b7280;
}

.countries-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.country-card {
    background: white;
    padding: 40px 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.country-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.country-flag {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px auto;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #e5e7eb;
}

.country-flag img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.country-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 15px;
}

.country-card p {
    font-size: 1rem;
    color: #6b7280;
    margin-bottom: 20px;
    line-height: 1.6;
}

.country-card ul {
    list-style: none;
    padding: 0;
    text-align: left;
}

.country-card ul li {
    padding: 5px 0;
    color: #4b5563;
    position: relative;
    padding-left: 20px;
}

.country-card ul li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #10b981;
    font-weight: bold;
}

/* Study Process Section */
.study-process-section {
    background: white;
    padding: 100px 0;
}

.study-process-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.process-step {
    text-align: center;
    position: relative;
}

.step-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 auto 20px auto;
    position: relative;
    z-index: 2;
}

.step-content h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 10px;
}

.step-content p {
    font-size: 1rem;
    color: #6b7280;
    line-height: 1.6;
}

/* Contact CTA Section */
.contact-cta-section {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    padding: 80px 0;
    color: white;
    text-align: center;
}

.contact-cta-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

.contact-cta-container h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.contact-cta-container p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.contact-cta-container .cta-button {
    background: white;
    color: #1e3a8a;
    padding: 15px 40px;
    border-radius: 30px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    font-size: 1.1rem;
}

.contact-cta-container .cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Study Hero Section - Y-Axis Style */
.study-hero-section {
    background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #3b82f6 100%);
    padding: 120px 0 80px 0;
    color: white;
    position: relative;
    overflow: hidden;
}

.study-hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.study-hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: start;
}

/* Study Page Mobile Responsive */
@media (max-width: 768px) {
    .study-hero-section {
        padding: 80px 0 60px 0;
    }

    .study-hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
        line-height: 1.2;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        margin: 30px 0;
    }

    .stat-item {
        padding: 15px 10px;
    }

    .stat-icon {
        width: 35px;
        height: 35px;
        margin-bottom: 8px;
    }

    .stat-icon i {
        font-size: 1rem;
    }

    .stat-number {
        font-size: 1.4rem;
    }

    .stat-label {
        font-size: 0.75rem;
    }

    .hero-visual {
        max-width: 100%;
    }

    .main-image {
        height: 250px;
        margin-bottom: 20px;
    }

    .floating-cards {
        margin-top: 20px;
    }

    .info-card {
        padding: 15px;
        gap: 10px;
    }

    .hero-features {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .feature-item {
        padding: 15px;
    }

    .cta-buttons {
        flex-direction: column;
        gap: 15px;
    }

    .primary-cta,
    .secondary-cta {
        justify-content: center;
        padding: 14px 28px;
    }

    /* Study Services Mobile */
    .study-services-section {
        padding: 60px 0;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-top: 40px;
    }

    .service-card {
        padding: 25px 20px;
        text-align: center;
    }

    .service-icon {
        width: 60px;
        height: 60px;
        margin: 0 auto 15px auto;
    }

    .service-icon i {
        font-size: 1.5rem;
    }

    .service-card h3 {
        font-size: 1.3rem;
        margin-bottom: 10px;
    }

    .service-card p {
        font-size: 0.9rem;
        margin-bottom: 15px;
    }

    .service-card ul {
        padding: 12px;
        margin-bottom: 20px;
    }

    .service-card ul li {
        font-size: 0.85rem;
        margin-bottom: 6px;
        padding-left: 20px;
    }

    .service-btn {
        padding: 12px 24px;
        font-size: 0.85rem;
    }

    .study-countries-section {
        padding: 60px 0;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .countries-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .country-card {
        padding: 30px 20px;
    }

    .study-process-section {
        padding: 60px 0;
    }

    .process-steps {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .contact-cta-section {
        padding: 60px 0;
    }

    .contact-cta-container h2 {
        font-size: 2rem;
    }

    .contact-cta-container p {
        font-size: 1.1rem;
    }
}

/* Y-Axis Style Migrate Page */

/* Migrate Hero Section - Y-Axis Style */
.migrate-hero-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
    padding: 120px 0;
    min-height: 700px;
    position: relative;
    overflow: hidden;
}

.migrate-hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="10" cy="90" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.migrate-hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.migrate-hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-left .hero-badge {
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    color: white;
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 30px;
}

.hero-left h1 {
    font-size: 3rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-left .hero-subtitle {
    font-size: 1.2rem;
    color: #6b7280;
    margin-bottom: 40px;
    line-height: 1.6;
}

/* Hero Form - Y-Axis Style */
.hero-form {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.hero-form h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 20px;
}

.form-row {
    margin-bottom: 15px;
}

.form-select,
.form-input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-select:focus,
.form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-submit {
    width: 100%;
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    color: white;
    padding: 15px;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.form-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(30, 58, 138, 0.3);
}

/* Hero Right Side */
.hero-right {
    position: relative;
}

.hero-image {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    position: relative;
    transition: all 0.3s ease;
}

.hero-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(30, 58, 138, 0.1), rgba(59, 130, 246, 0.1));
    z-index: 1;
}

.hero-image:hover {
    transform: translateY(-5px);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
}

.hero-image img {
    width: 100%;
    height: 450px;
    object-fit: cover;
    transition: all 0.3s ease;
}

.hero-image:hover img {
    transform: scale(1.05);
}

.confused-card {
    position: absolute;
    bottom: -40px;
    right: 30px;
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
    text-align: center;
    min-width: 220px;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.confused-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.25);
}

.confused-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #f59e0b, #f97316);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px auto;
}

.confused-icon i {
    font-size: 1.5rem;
    color: white;
}

.confused-card h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 5px;
}

.confused-card p {
    font-size: 0.9rem;
    color: #6b7280;
    margin-bottom: 15px;
}

.consult-btn {
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    color: white;
    padding: 8px 20px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.consult-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(30, 58, 138, 0.3);
}

/* Enhanced Migrate Opportunity Section */
.migrate-opportunity-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
    padding: 100px 0;
}

.migrate-opportunity-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

.destinations-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 60px;
}

.destination-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.destination-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
}

.destination-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.destination-flag {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px auto;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #e5e7eb;
    transition: all 0.3s ease;
}

.destination-flag img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.destination-card:hover .destination-flag {
    border-color: #3b82f6;
    transform: scale(1.1);
}

.destination-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 15px;
}

.destination-features {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
    margin-bottom: 20px;
}

.feature-tag {
    background: linear-gradient(135deg, #e0e7ff, #c7d2fe);
    color: #1e3a8a;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    border: 1px solid #c7d2fe;
}

.destination-card p {
    color: #6b7280;
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 25px;
}

.destination-btn {
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: inline-block;
}

.destination-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(30, 58, 138, 0.3);
}

/* Study Opportunity Section - Same as Migrate */
.study-opportunity-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
    padding: 100px 0;
}

.study-opportunity-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

.study-destinations-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 60px;
}

.study-destination-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.study-destination-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
}

.study-destination-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.study-destination-card .destination-flag {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px auto;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #e5e7eb;
    transition: all 0.3s ease;
}

.study-destination-card .destination-flag img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.study-destination-card:hover .destination-flag {
    border-color: #3b82f6;
    transform: scale(1.1);
}

.study-destination-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 15px;
}

.study-destination-card .destination-features {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
    margin-bottom: 20px;
}

.study-destination-card .feature-tag {
    background: linear-gradient(135deg, #e0e7ff, #c7d2fe);
    color: #1e3a8a;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    border: 1px solid #c7d2fe;
}

.study-destination-card p {
    color: #6b7280;
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 25px;
}

.study-destination-card .destination-btn {
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: inline-block;
}

.study-destination-card .destination-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(30, 58, 138, 0.3);
}

/* Work Opportunity Section - Same as Migrate */
.work-opportunity-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
    padding: 100px 0;
}

.work-opportunity-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

.work-destinations-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 60px;
}

.work-destination-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.work-destination-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #dc2626, #ef4444);
}

.work-destination-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.work-destination-card .destination-flag {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px auto;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #e5e7eb;
    transition: all 0.3s ease;
}

.work-destination-card .destination-flag img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.work-destination-card:hover .destination-flag {
    border-color: #ef4444;
    transform: scale(1.1);
}

.work-destination-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #dc2626;
    margin-bottom: 15px;
}

.work-destination-card .destination-features {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
    margin-bottom: 20px;
}

.work-destination-card .feature-tag {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    color: #dc2626;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    border: 1px solid #fecaca;
}

.work-destination-card p {
    color: #6b7280;
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 25px;
}

.work-destination-card .destination-btn {
    background: linear-gradient(135deg, #dc2626, #ef4444);
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: inline-block;
}

.work-destination-card .destination-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(220, 38, 38, 0.3);
}

/* Visit Opportunity Section - Same as Migrate */
.visit-opportunity-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
    padding: 100px 0;
}

.visit-opportunity-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

.visit-destinations-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 60px;
}

.visit-destination-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.visit-destination-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #7c3aed, #a855f7);
}

.visit-destination-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.visit-destination-card .destination-flag {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px auto;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #e5e7eb;
    transition: all 0.3s ease;
}

.visit-destination-card .destination-flag img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.visit-destination-card:hover .destination-flag {
    border-color: #a855f7;
    transform: scale(1.1);
}

.visit-destination-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #7c3aed;
    margin-bottom: 15px;
}

.visit-destination-card .destination-features {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
    margin-bottom: 20px;
}

.visit-destination-card .feature-tag {
    background: linear-gradient(135deg, #f3e8ff, #e9d5ff);
    color: #7c3aed;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    border: 1px solid #e9d5ff;
}

.visit-destination-card p {
    color: #6b7280;
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 25px;
}

.visit-destination-card .destination-btn {
    background: linear-gradient(135deg, #7c3aed, #a855f7);
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: inline-block;
}

.visit-destination-card .destination-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(124, 58, 237, 0.3);
}

/* Study Services Section - Enhanced */
.study-services-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
    padding: 80px 0;
    position: relative;
}

.study-services-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(59,130,246,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.5;
}

.study-services-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
    margin-top: 50px;
}

.service-card {
    background: white;
    border-radius: 20px;
    padding: 35px 25px;
    text-align: left;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(59, 130, 246, 0.1);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(135deg, #3b82f6, #60a5fa, #34d399);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.2);
}

.service-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    transition: all 0.4s ease;
    position: relative;
}

.service-icon::after {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(135deg, #3b82f6, #60a5fa, #34d399);
    border-radius: 18px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.service-card:hover .service-icon::after {
    opacity: 1;
}

.service-icon i {
    font-size: 1.8rem;
    color: white;
}

.service-card:hover .service-icon {
    transform: scale(1.05) rotate(5deg);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

.service-card h3 {
    font-size: 1.4rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 12px;
    line-height: 1.3;
}

.service-card p {
    color: #6b7280;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 20px;
}

.service-card ul {
    list-style: none;
    padding: 0;
    margin-bottom: 25px;
    background: #f8fafc;
    border-radius: 12px;
    padding: 15px;
}

.service-card ul li {
    color: #4b5563;
    font-size: 0.9rem;
    margin-bottom: 8px;
    padding-left: 25px;
    position: relative;
    line-height: 1.5;
}

.service-card ul li:last-child {
    margin-bottom: 0;
}

.service-card ul li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 6px;
    width: 16px;
    height: 16px;
    background: linear-gradient(135deg, #10b981, #34d399);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.service-card ul li::after {
    content: '✓';
    position: absolute;
    left: 4px;
    top: 6px;
    color: white;
    font-size: 0.7rem;
    font-weight: bold;
}

.service-btn {
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    color: white;
    padding: 14px 28px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.4s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
}

.service-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.service-btn:hover::before {
    left: 100%;
}

.service-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.4);
    background: linear-gradient(135deg, #2563eb, #3b82f6);
}

.service-btn::after {
    content: '→';
    font-size: 1rem;
    transition: transform 0.3s ease;
}

.service-btn:hover::after {
    transform: translateX(3px);
}

/* Study Process Timeline */
.study-process-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
    padding: 100px 0;
}

.study-process-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.process-timeline {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 60px;
}

.timeline-step {
    background: white;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    position: relative;
}

.timeline-step:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.step-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px auto;
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    transition: all 0.3s ease;
}

.timeline-step:hover .step-number {
    transform: scale(1.1);
    box-shadow: 0 8px 20px rgba(30, 58, 138, 0.3);
}

.step-content h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 15px;
}

.step-content p {
    color: #6b7280;
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 20px;
}

.step-features {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

.step-features span {
    background: linear-gradient(135deg, #e0e7ff, #c7d2fe);
    color: #1e3a8a;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    border: 1px solid #c7d2fe;
}

/* University Placements Section */
.university-placements-section {
    background: white;
    padding: 100px 0;
}

.placements-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.universities-by-country {
    margin-top: 60px;
}

.country-universities {
    margin-bottom: 50px;
}

.country-universities h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 30px;
    text-align: center;
}

.university-logos {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
}

.university-logo {
    text-align: center;
    padding: 20px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.university-logo:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
}

.university-logo img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 15px;
}

.university-logo span {
    font-size: 0.9rem;
    font-weight: 600;
    color: #374151;
    display: block;
}

/* Student Success Stories Section */
.student-success-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
    padding: 100px 0;
}

.success-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.success-stories-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 60px;
}

.success-story {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    text-align: center;
}

.success-story:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.student-photo {
    width: 100px;
    height: 100px;
    margin: 0 auto 20px auto;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid #1e3a8a;
}

.student-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.story-content h4 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 5px;
}

.university {
    font-size: 1rem;
    color: #6b7280;
    font-weight: 600;
    margin-bottom: 15px;
}

.testimonial {
    font-size: 1rem;
    color: #4b5563;
    line-height: 1.6;
    margin-bottom: 20px;
    font-style: italic;
}

.story-tags {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

.story-tags span {
    background: linear-gradient(135deg, #e0e7ff, #c7d2fe);
    color: #1e3a8a;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    border: 1px solid #c7d2fe;
}

/* Check Eligibility Section */
.check-eligibility-section {
    background: #f8fafc;
    padding: 80px 0;
}

.eligibility-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.eligibility-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.eligibility-left img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.eligibility-right h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 20px;
}

.eligibility-right p {
    font-size: 1.2rem;
    color: #6b7280;
    margin-bottom: 30px;
    line-height: 1.6;
}

.eligibility-btn {
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    color: white;
    padding: 15px 30px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
}

.eligibility-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(30, 58, 138, 0.3);
}

.eligibility-btn i {
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.eligibility-btn:hover i {
    transform: translateX(5px);
}

/* Migration Process Section - Y-Axis Style */
.migration-process-section {
    background: white;
    padding: 80px 0;
}

.migration-process-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.process-flow {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 20px;
    margin-top: 50px;
}

.process-step {
    text-align: center;
    position: relative;
}

.step-image {
    width: 130px;
    height: 130px;
    margin: 0 auto 25px auto;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid #e5e7eb;
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.step-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(59, 130, 246, 0.2), rgba(30, 58, 138, 0.2));
    z-index: 1;
    opacity: 0;
    transition: all 0.3s ease;
}

.step-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.process-step:hover .step-image {
    border-color: #3b82f6;
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(59, 130, 246, 0.3);
}

.process-step:hover .step-image::before {
    opacity: 1;
}

.process-step:hover .step-image img {
    transform: scale(1.1);
}

.process-step h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 10px;
}

.process-step p {
    font-size: 0.9rem;
    color: #6b7280;
    line-height: 1.5;
}

.step-arrow {
    position: absolute;
    top: 50px;
    right: -30px;
    color: #3b82f6;
    font-size: 2rem;
}

.process-step:last-child .step-arrow {
    display: none;
}

/* Get Evaluated Section */
.get-evaluated-section {
    background: #f8fafc;
    padding: 80px 0;
}

.evaluated-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.evaluated-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;
    align-items: start;
}

.evaluated-left h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 20px;
}

.evaluated-left p {
    font-size: 1.1rem;
    color: #6b7280;
    margin-bottom: 40px;
    line-height: 1.6;
}

.evaluation-features {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 40px;
}

.eval-feature {
    text-align: center;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.eval-feature:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
}

.eval-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #3b82f6, #1e3a8a);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px auto;
    transition: all 0.3s ease;
}

.eval-icon i {
    font-size: 1.8rem;
    color: white;
}

.eval-feature:hover .eval-icon {
    transform: scale(1.1);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
}

.eval-feature span {
    font-size: 0.9rem;
    font-weight: 600;
    color: #1e3a8a;
}

.get-evaluated-btn {
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    color: white;
    padding: 15px 30px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
}

.get-evaluated-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(30, 58, 138, 0.3);
}

.get-evaluated-btn i {
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.get-evaluated-btn:hover i {
    transform: translateX(5px);
}

/* Did You Know Section */
.did-you-know {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.bulb-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px auto;
    border-radius: 50%;
    background: linear-gradient(135deg, #f59e0b, #f97316);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.bulb-icon i {
    font-size: 2.5rem;
    color: white;
}

.did-you-know:hover .bulb-icon {
    transform: scale(1.1);
    box-shadow: 0 10px 25px rgba(245, 158, 11, 0.4);
}

.did-you-know h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 15px;
}

.did-you-know p {
    font-size: 1rem;
    color: #6b7280;
    margin-bottom: 25px;
    line-height: 1.6;
}

.news-links {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.news-links a {
    color: #3b82f6;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 5px 0;
    border-bottom: 1px solid #f1f5f9;
}

.news-links a:hover {
    color: #1e3a8a;
    font-weight: 600;
}

/* Migration Benefits Section */
.migration-benefits-section {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    padding: 100px 0;
    color: white;
}

.benefits-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.migration-benefits-section .section-header h2 {
    color: white;
    font-size: 2.8rem;
    margin-bottom: 15px;
}

.migration-benefits-section .section-header p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.2rem;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin: 60px 0;
}

.benefit-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 30px 20px;
    border-radius: 15px;
    text-align: center;
    transition: all 0.3s ease;
}

.benefit-card:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.benefit-icon {
    width: 70px;
    height: 70px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px auto;
    transition: all 0.3s ease;
}

.benefit-icon i {
    font-size: 2rem;
    color: white;
}

.benefit-card:hover .benefit-icon {
    background: white;
    transform: scale(1.1);
}

.benefit-card:hover .benefit-icon i {
    color: #1e3a8a;
}

.benefit-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: white;
}

.benefit-card p {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.5;
}

/* Migration Stats */
.migration-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    margin-top: 60px;
    padding-top: 60px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 3.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

/* Visit Hero Section - Y-Axis Style */
.visit-hero-section {
    background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #3b82f6 100%);
    padding: 120px 0 80px 0;
    color: white;
    position: relative;
    overflow: hidden;
}

.visit-hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.visit-hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: start;
}

/* Visit Services Section */
.visit-services-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
    padding: 80px 0;
    position: relative;
}

.visit-services-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(59,130,246,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.5;
}

.visit-services-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.service-countries {
    margin: 15px 0;
    padding-top: 15px;
    border-top: 1px solid #e5e7eb;
}

.service-countries span {
    font-size: 0.85rem;
    color: #6b7280;
    font-weight: 600;
    display: block;
    margin-bottom: 8px;
}

.country-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.country-tags span {
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    color: white;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Destinations Section */
.destinations-section {
    background: white;
    padding: 80px 0;
}

.destinations-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.destinations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.destination-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.4s ease;
}

.destination-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.destination-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.destination-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.destination-card:hover .destination-image img {
    transform: scale(1.1);
}

.destination-overlay {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.destination-flag {
    font-size: 1.5rem;
}

.destination-content {
    padding: 25px;
}

.destination-content h3 {
    font-size: 1.4rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 15px;
}

.visa-types {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 20px;
}

.visa-tag {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    color: #1e40af;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.destination-stats {
    margin-bottom: 20px;
}

.stat {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f3f4f6;
}

.stat:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.stat-label {
    font-size: 0.9rem;
    color: #6b7280;
    font-weight: 500;
}

.stat-value {
    font-size: 0.9rem;
    color: #1e3a8a;
    font-weight: 600;
}

.destination-btn {
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: inline-block;
    width: 100%;
    text-align: center;
}

.destination-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

/* Migrate Hero Section - Y-Axis Style */
.migrate-hero-section {
    background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #3b82f6 100%);
    padding: 120px 0 80px 0;
    color: white;
    position: relative;
    overflow: hidden;
}

.migrate-hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.migrate-hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: start;
}

/* Work Hero Section - Y-Axis Style */
.work-hero-section {
    background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #3b82f6 100%);
    padding: 120px 0 80px 0;
    color: white;
    position: relative;
    overflow: hidden;
}

.work-hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.work-hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: start;
}

/* Y-Axis Style Mobile Responsive */
@media (max-width: 768px) {
    .migrate-hero-section,
    .study-hero-section,
    .work-hero-section,
    .visit-hero-section {
        padding: 60px 0;
    }

    .migrate-hero-content,
    .work-hero-content,
    .study-hero-content {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-features {
        justify-content: center;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .primary-cta, .secondary-cta {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }

    .hero-visual {
        margin-top: 30px;
    }

    .main-image img {
        height: 300px;
    }

    .floating-cards {
        display: none;
    }
}

/* Common Hero Elements for All Pages */
.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    pointer-events: none;
}

.floating-icon {
    position: absolute;
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    animation: float 8s ease-in-out infinite;
    opacity: 0.6;
}

.floating-icon i {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.8);
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.hero-left {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50px;
    padding: 12px 20px;
    font-size: 0.9rem;
    font-weight: 600;
    color: white;
    margin-bottom: 25px;
    width: fit-content;
    transition: all 0.3s ease;
}

.hero-badge:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.badge-icon {
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #60a5fa, #34d399);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.badge-icon i {
    font-size: 0.8rem;
    color: white;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 25px;
    color: white;
}

.title-main {
    display: block;
    color: white;
}

.title-highlight {
    display: block;
    background: linear-gradient(135deg, #60a5fa, #34d399);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 35px;
    max-width: 600px;
}

.hero-features {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 40px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 10px 18px;
    font-size: 0.9rem;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.feature-icon {
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #60a5fa, #34d399);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.feature-icon i {
    font-size: 0.7rem;
    color: white;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 12px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 15px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-3px);
}

.stat-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #60a5fa, #34d399);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.stat-icon i {
    font-size: 1rem;
    color: white;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 1.4rem;
    font-weight: 800;
    color: white;
    line-height: 1;
}

.stat-label {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
    margin-top: 2px;
}

.hero-cta-section {
    margin-top: 40px;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.primary-cta, .secondary-cta {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    text-decoration: none;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
}

.primary-cta {
    background: linear-gradient(135deg, #60a5fa, #34d399);
    color: white;
    box-shadow: 0 8px 25px rgba(96, 165, 250, 0.3);
}

.primary-cta:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(96, 165, 250, 0.4);
    background: linear-gradient(135deg, #3b82f6, #10b981);
}

.secondary-cta {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.secondary-cta:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-3px);
}

.cta-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cta-note {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

.cta-note i {
    font-size: 0.9rem;
    color: #34d399;
}

.hero-right {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.hero-visual {
    position: relative;
    width: 100%;
    max-width: 500px;
}

.main-image {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.main-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.main-image:hover img {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(52, 211, 153, 0.2));
    display: flex;
    align-items: flex-end;
    padding: 25px;
}

.success-badge {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 50px;
    padding: 12px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    color: #1e3a8a;
    font-size: 0.9rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.success-badge i {
    color: #f59e0b;
    font-size: 1rem;
}

.floating-cards {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.info-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 15px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    animation: cardFloat 6s ease-in-out infinite;
    max-width: 200px;
}

.card-1 {
    top: 20px;
    right: -50px;
    animation-delay: 0s;
}

.card-2 {
    bottom: 120px;
    left: -60px;
    animation-delay: 2s;
}

.card-3 {
    bottom: 20px;
    right: -40px;
    animation-delay: 4s;
}

@keyframes cardFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
}

.card-icon {
    width: 35px;
    height: 35px;
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.card-icon i {
    font-size: 0.9rem;
    color: white;
}

.card-content h4 {
    font-size: 0.85rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 2px;
    line-height: 1.2;
}

.card-content p {
    font-size: 0.75rem;
    color: #6b7280;
    font-weight: 500;
    line-height: 1.3;
}

    .visit-hero-content {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }

    .visit-services-section {
        padding: 60px 0;
    }

    .destinations-section {
        padding: 60px 0;
    }

    .destinations-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-top: 40px;
    }

    .destination-card {
        margin: 0 10px;
    }

    .destination-image {
        height: 180px;
    }

    .destination-content {
        padding: 20px;
    }

    .destination-content h3 {
        font-size: 1.2rem;
    }

    .visa-types {
        margin-bottom: 15px;
    }

    .visa-tag {
        font-size: 0.75rem;
        padding: 5px 10px;
    }

    .migrate-hero-content,
    .study-hero-content,
    .work-hero-content,
    .visit-hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .hero-left h1 {
        font-size: 2.5rem;
    }

    .hero-left .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-form {
        padding: 25px;
    }

    .hero-form h3 {
        font-size: 1.2rem;
    }

    .form-select,
    .form-input {
        padding: 10px 12px;
        font-size: 0.9rem;
    }

    .form-submit {
        padding: 12px;
        font-size: 1rem;
    }

    .hero-image img {
        height: 300px;
    }

    .confused-card {
        position: static;
        margin-top: 20px;
        padding: 15px;
        min-width: auto;
    }

    .confused-icon {
        width: 40px;
        height: 40px;
        margin-bottom: 10px;
    }

    .confused-icon i {
        font-size: 1.2rem;
    }

    .confused-card h4 {
        font-size: 1.1rem;
    }

    .confused-card p {
        font-size: 0.8rem;
        margin-bottom: 10px;
    }

    .consult-btn {
        padding: 6px 15px;
        font-size: 0.8rem;
    }

    /* All Opportunity Sections Mobile */
    .destinations-grid,
    .study-destinations-grid,
    .work-destinations-grid,
    .visit-destinations-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .destination-card,
    .study-destination-card,
    .work-destination-card,
    .visit-destination-card {
        padding: 25px;
    }

    .destination-flag {
        width: 70px;
        height: 70px;
    }

    .destination-features {
        gap: 6px;
        margin-bottom: 15px;
    }

    .feature-tag {
        padding: 3px 10px;
        font-size: 0.75rem;
    }

    .destination-btn {
        padding: 10px 20px;
        font-size: 0.85rem;
    }

    /* Check Eligibility Mobile */
    .eligibility-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .eligibility-right h2 {
        font-size: 2rem;
    }

    .eligibility-right p {
        font-size: 1.1rem;
    }

    /* Process Flow Mobile */
    .process-flow {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .step-arrow {
        display: none;
    }

    .step-image {
        width: 100px;
        height: 100px;
    }

    /* Get Evaluated Mobile */
    .evaluated-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .evaluated-left h2 {
        font-size: 2rem;
    }

    .evaluation-features {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .eval-feature {
        padding: 15px;
    }

    .eval-icon {
        width: 50px;
        height: 50px;
    }

    .eval-icon i {
        font-size: 1.5rem;
    }

    .eval-feature span {
        font-size: 0.8rem;
    }

    .did-you-know {
        padding: 25px;
    }

    .bulb-icon {
        width: 60px;
        height: 60px;
    }

    .bulb-icon i {
        font-size: 2rem;
    }

    .did-you-know h3 {
        font-size: 1.3rem;
    }

    .did-you-know p {
        font-size: 0.9rem;
    }

    .news-links a {
        font-size: 0.8rem;
    }

    /* Benefits Section Mobile */
    .benefits-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
        margin: 40px 0;
    }

    .benefit-card {
        padding: 25px 15px;
    }

    .benefit-icon {
        width: 60px;
        height: 60px;
    }

    .benefit-icon i {
        font-size: 1.8rem;
    }

    .benefit-card h3 {
        font-size: 1.2rem;
    }

    .benefit-card p {
        font-size: 0.9rem;
    }

    .migration-stats {
        grid-template-columns: 1fr;
        gap: 30px;
        margin-top: 40px;
        padding-top: 40px;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .stat-label {
        font-size: 1rem;
    }

    .migration-benefits-section .section-header h2 {
        font-size: 2.2rem;
    }

    .migration-benefits-section .section-header p {
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .benefits-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .benefit-card {
        padding: 20px 15px;
    }

    .benefit-icon {
        width: 50px;
        height: 50px;
    }

    .benefit-icon i {
        font-size: 1.5rem;
    }

    .benefit-card h3 {
        font-size: 1.1rem;
    }

    .benefit-card p {
        font-size: 0.85rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .stat-label {
        font-size: 0.9rem;
    }

    .migration-benefits-section .section-header h2 {
        font-size: 1.8rem;
    }

    /* Study Services Mobile */
    .services-grid {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .service-card {
        padding: 25px;
    }

    .service-icon {
        width: 70px;
        height: 70px;
    }

    .service-icon i {
        font-size: 1.8rem;
    }

    .service-card h3 {
        font-size: 1.3rem;
    }

    .service-card p {
        font-size: 0.9rem;
    }

    .service-btn {
        padding: 10px 20px;
        font-size: 0.85rem;
    }

    /* Study Process Mobile */
    .process-timeline {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .timeline-step {
        padding: 25px;
    }

    .step-number {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
    }

    .step-content h3 {
        font-size: 1.2rem;
    }

    .step-content p {
        font-size: 0.9rem;
    }

    .step-features span {
        font-size: 0.75rem;
        padding: 3px 10px;
    }

    /* University Placements Mobile */
    .university-logos {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .university-logo {
        padding: 15px;
    }

    .university-logo img {
        width: 60px;
        height: 60px;
    }

    .university-logo span {
        font-size: 0.8rem;
    }

    .country-universities h3 {
        font-size: 1.5rem;
    }

    /* Success Stories Mobile */
    .success-stories-grid {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .success-story {
        padding: 25px;
    }

    .student-photo {
        width: 80px;
        height: 80px;
    }

    .story-content h4 {
        font-size: 1.2rem;
    }

    .university {
        font-size: 0.9rem;
    }

    .testimonial {
        font-size: 0.9rem;
    }

    .story-tags span {
        font-size: 0.75rem;
        padding: 3px 10px;
    }

    /* Study Hero Mobile */
    .study-hero-section {
        min-height: auto;
        padding: 100px 0 80px 0;
    }

    .floating-elements {
        display: none;
    }

    .study-hero-content {
        grid-template-columns: 1fr;
        gap: 50px;
        text-align: center;
    }

    .hero-title {
        font-size: 2.8rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
        max-width: 100%;
    }

    .hero-features {
        align-items: center;
    }

    .hero-stats {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 25px 0;
    }

    .stat-item {
        padding: 15px;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .stat-label {
        font-size: 0.85rem;
    }

    .cta-buttons {
        flex-direction: column;
        gap: 15px;
    }

    .primary-cta,
    .secondary-cta {
        padding: 14px 28px;
        font-size: 0.95rem;
        justify-content: center;
    }

    .hero-visual {
        height: 400px;
    }

    .main-image {
        height: 350px;
    }

    .floating-cards {
        display: none;
    }

    /* Quick Assessment Mobile */
    .form-grid {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 30px 20px;
    }

    .assessment-header h3 {
        font-size: 1.8rem;
    }

    .assessment-header p {
        font-size: 1rem;
    }

    .form-group input,
    .form-group select {
        padding: 12px;
        font-size: 0.9rem;
    }

    .assessment-submit {
        padding: 12px 20px;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .study-hero-section {
        padding: 80px 0 60px 0;
    }

    .hero-badge {
        padding: 10px 20px;
        font-size: 0.8rem;
    }

    .badge-icon {
        width: 28px;
        height: 28px;
        font-size: 0.9rem;
    }

    .hero-title {
        font-size: 2.2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .feature-item {
        font-size: 1rem;
    }

    .hero-stats {
        gap: 12px;
        padding: 20px 0;
    }

    .stat-item {
        padding: 12px;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
    }

    .stat-icon i {
        font-size: 1.2rem;
    }

    .stat-number {
        font-size: 1.6rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    .primary-cta,
    .secondary-cta {
        padding: 12px 24px;
        font-size: 0.9rem;
    }

    .cta-note {
        font-size: 0.8rem;
    }

    .hero-visual {
        height: 300px;
    }

    .main-image {
        height: 250px;
        border-radius: 20px;
    }

    .success-badge {
        padding: 10px 16px;
        font-size: 0.8rem;
    }

    .form-grid {
        padding: 20px 15px;
    }

    .assessment-header h3 {
        font-size: 1.5rem;
    }

    .form-group input,
    .form-group select {
        padding: 10px;
        font-size: 0.85rem;
    }

    .assessment-submit {
        padding: 10px 15px;
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .services-grid {
        gap: 20px;
    }

    .service-card {
        padding: 20px;
    }

    .service-icon {
        width: 60px;
        height: 60px;
    }

    .service-icon i {
        font-size: 1.5rem;
    }

    .service-card h3 {
        font-size: 1.2rem;
    }

    .service-card p {
        font-size: 0.85rem;
    }

    .service-btn {
        padding: 8px 16px;
        font-size: 0.8rem;
    }

    .process-timeline {
        gap: 20px;
    }

    .timeline-step {
        padding: 20px;
    }

    .step-number {
        width: 45px;
        height: 45px;
        font-size: 1.2rem;
    }

    .university-logos {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .university-logo {
        padding: 12px;
    }

    .university-logo img {
        width: 50px;
        height: 50px;
    }

    .university-logo span {
        font-size: 0.75rem;
    }

    .success-stories-grid {
        gap: 20px;
    }

    .success-story {
        padding: 20px;
    }

    .student-photo {
        width: 70px;
        height: 70px;
    }
}

@media (max-width: 480px) {
    .hero-left h1 {
        font-size: 2rem;
    }

    .hero-left .hero-subtitle {
        font-size: 1rem;
    }

    .hero-form {
        padding: 20px;
    }

    .hero-form h3 {
        font-size: 1.1rem;
    }

    .form-row {
        margin-bottom: 12px;
    }

    .form-select,
    .form-input {
        padding: 8px 10px;
        font-size: 0.85rem;
    }

    .form-submit {
        padding: 10px;
        font-size: 0.9rem;
    }
}